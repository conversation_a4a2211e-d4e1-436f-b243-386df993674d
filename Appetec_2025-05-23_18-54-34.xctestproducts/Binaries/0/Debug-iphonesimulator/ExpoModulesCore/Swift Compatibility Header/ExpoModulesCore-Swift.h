#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 6.1.2 effective-5.10 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)
#ifndef EXPOMODULESCORE_SWIFT_H
#define EXPOMODULESCORE_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef unsigned char char8_t;
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import CoreFoundation;
@import Foundation;
@import ObjectiveC;
@import React;
@import React_RCTAppDelegate;
@import UIKit;
#endif

#import <ExpoModulesCore/ExpoModulesCore.h>

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="ExpoModulesCore",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

@class EXModuleRegistry;
@class EXNativeModulesProxy;
@class RCTBridge;
@class EXRuntime;
@class NSString;
@class ViewModuleWrapper;
@class EXJavaScriptObject;
@class EXModulesProxyConfig;
@class ModulesProvider;
/// The app context is an interface to a single Expo app.
SWIFT_CLASS_NAMED("AppContext")
@interface EXAppContext : NSObject
/// The legacy module registry with modules written in the old-fashioned way.
@property (nonatomic, weak) EXModuleRegistry * _Nullable legacyModuleRegistry;
@property (nonatomic, weak) EXNativeModulesProxy * _Nullable legacyModulesProxy;
/// React bridge of the context’s app. Can be <code>nil</code> when the bridge
/// hasn’t been propagated to the bridge modules yet (see <code>ExpoBridgeModule</code>),
/// or when the app context is “bridgeless” (for example in native unit tests).
@property (nonatomic, weak) RCTBridge * _Nullable reactBridge;
/// Underlying JSI runtime of the running app.
@property (nonatomic, strong) EXRuntime * _Nullable _runtime;
/// The application identifier that is used to distinguish between different <code>RCTHost</code>.
/// It might be equal to <code>nil</code>, meaning we couldn’t obtain the Id for the current app.
/// It shouldn’t be used on the old architecture.
@property (nonatomic, readonly, copy) NSString * _Nullable appIdentifier;
- (nonnull instancetype)init;
- (nonnull instancetype)useModulesProvider:(NSString * _Nonnull)providerName;
/// Returns view modules wrapped by the base <code>ViewModuleWrapper</code> class.
- (NSArray<ViewModuleWrapper *> * _Nonnull)getViewManagers SWIFT_WARN_UNUSED_RESULT;
/// Returns a bool whether the module with given name is registered in this context.
- (BOOL)hasModule:(NSString * _Nonnull)moduleName SWIFT_WARN_UNUSED_RESULT;
/// Returns an array of names of the modules registered in the module registry.
- (NSArray<NSString *> * _Nonnull)getModuleNames SWIFT_WARN_UNUSED_RESULT;
/// Returns a JavaScript object that represents a module with given name.
/// When remote debugging is enabled, this will always return <code>nil</code>.
- (EXJavaScriptObject * _Nullable)getNativeModuleObject:(NSString * _Nonnull)moduleName SWIFT_WARN_UNUSED_RESULT;
/// Returns an array of event names supported by all Swift modules.
- (NSArray<NSString *> * _Nonnull)getSupportedEvents SWIFT_WARN_UNUSED_RESULT;
/// Modifies listeners count for module with given name. Depending on the listeners count,
/// <code>onStartObserving</code> and <code>onStopObserving</code> are called.
- (void)modifyEventListenersCount:(NSString * _Nonnull)moduleName count:(NSInteger)count;
/// Asynchronously calls module’s function with given arguments.
- (void)callFunction:(NSString * _Nonnull)functionName onModule:(NSString * _Nonnull)moduleName withArgs:(NSArray * _Nonnull)args resolve:(EXPromiseResolveBlock _Nonnull)resolve reject:(EXPromiseRejectBlock _Nonnull)reject;
@property (nonatomic, strong) EXModulesProxyConfig * _Nullable expoModulesConfig;
/// Returns an instance of the generated Expo modules provider.
/// The provider is usually generated in application’s <code>ExpoModulesProviders</code> files group.
+ (ModulesProvider * _Nonnull)modulesProviderWithName:(NSString * _Nonnull)providerName SWIFT_WARN_UNUSED_RESULT;
@end

/// Base class for app delegate subscribers. Ensures the class
/// inherits from <code>UIResponder</code> and has <code>required init()</code> initializer.
SWIFT_CLASS_NAMED("BaseExpoAppDelegateSubscriber")
@interface EXBaseAppDelegateSubscriber : UIResponder
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@protocol RCTComponent;
@protocol RCTEventDispatcherProtocol;
/// Custom component data extending <code>RCTComponentData</code>. Its main purpose is to handle event-based props (callbacks),
/// but it also simplifies capturing the view config so we can omit some reflections that React Native executes.
SWIFT_CLASS_NAMED("ComponentData")
@interface EXComponentData : RCTComponentDataSwiftAdapter
/// Initializer that additionally takes the original view module to have access to its definition.
- (nonnull instancetype)initWithViewModule:(ViewModuleWrapper * _Nonnull)viewModule managerClass:(SWIFT_METATYPE(ViewModuleWrapper) _Nonnull)managerClass bridge:(RCTBridge * _Nonnull)bridge OBJC_DESIGNATED_INITIALIZER;
/// Creates a setter for the specific prop. For non-event props we just let React Native do its job.
/// Events are handled differently to conveniently use them in Swift.
- (RCTPropBlockAlias _Nonnull)createPropBlock:(NSString * _Nonnull)propName isShadowView:(BOOL)isShadowView SWIFT_WARN_UNUSED_RESULT;
- (void)setProps:(NSDictionary<NSString *, id> * _Nonnull)props forView:(id <RCTComponent> _Nonnull)view;
/// The base <code>RCTComponentData</code> class does some Objective-C dynamic calls in this function, but we don’t
/// need to do these slow operations since the Sweet API gives us necessary details without reflections.
- (NSDictionary<NSString *, id> * _Nonnull)viewConfig SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithManagerClass:(Class _Nonnull)managerClass bridge:(RCTBridge * _Nonnull)bridge eventDispatcher:(id <RCTEventDispatcherProtocol> _Nullable)eventDispatcher SWIFT_UNAVAILABLE;
@end

@class EXReactDelegate;
@class UIViewController;
@class NSURL;
@class RCTRootViewFactory;
SWIFT_CLASS_NAMED("ExpoAppInstance")
@interface EXAppInstance : RCTAppDelegate
- (nonnull instancetype)initWithAppDelegate:(RCTAppDelegate * _Nonnull)appDelegate;
@property (nonatomic, readonly, strong) EXReactDelegate * _Nonnull reactDelegate;
- (UIViewController * _Nonnull)createRootViewController SWIFT_WARN_UNUSED_RESULT;
- (NSURL * _Nullable)bundleURL SWIFT_WARN_UNUSED_RESULT;
- (RCTRootViewFactory * _Nonnull)createRCTRootViewFactory SWIFT_WARN_UNUSED_RESULT;
- (NSURL * _Nullable)sourceURLForBridge:(RCTBridge * _Nonnull)bridge SWIFT_WARN_UNUSED_RESULT;
+ (void)registerReactDelegateHandlersFromModulesProvider:(ModulesProvider * _Nonnull)modulesProvider;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class UIApplication;
@class NSData;
@class NSUserActivity;
@protocol UIUserActivityRestoring;
@class UIApplicationShortcutItem;
@class UIWindow;
@class RCTRootView;
@protocol EXAppDelegateSubscriberProtocol;
/// Allows classes extending <code>ExpoAppDelegateSubscriber</code> to hook into project’s app delegate
/// by forwarding <code>UIApplicationDelegate</code> events to the subscribers.
/// Keep functions and markers in sync with https://developer.apple.com/documentation/uikit/uiapplicationdelegate
SWIFT_CLASS_NAMED("ExpoAppDelegate")
@interface EXExpoAppDelegate : EXAppInstance
/// Whether to skip calling the React Native instance setup from <code>RCTAppDelegate</code>.
/// Set this property to <code>false</code> if your app delegate is not supposed to initialize a React Native app,
/// but only to handle the app delegate subscribers.
@property (nonatomic) BOOL shouldCallReactNativeSetup;
- (BOOL)application:(UIApplication * _Nonnull)application willFinishLaunchingWithOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions SWIFT_WARN_UNUSED_RESULT;
- (BOOL)application:(UIApplication * _Nonnull)application didFinishLaunchingWithOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions SWIFT_WARN_UNUSED_RESULT;
- (void)applicationDidBecomeActive:(UIApplication * _Nonnull)application;
- (void)applicationWillResignActive:(UIApplication * _Nonnull)application;
- (void)applicationDidEnterBackground:(UIApplication * _Nonnull)application;
- (void)applicationWillEnterForeground:(UIApplication * _Nonnull)application;
- (void)applicationWillTerminate:(UIApplication * _Nonnull)application;
- (void)application:(UIApplication * _Nonnull)application handleEventsForBackgroundURLSession:(NSString * _Nonnull)identifier completionHandler:(void (^ _Nonnull)(void))completionHandler;
- (void)application:(UIApplication * _Nonnull)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData * _Nonnull)deviceToken;
- (void)application:(UIApplication * _Nonnull)application didFailToRegisterForRemoteNotificationsWithError:(NSError * _Nonnull)error;
- (void)application:(UIApplication * _Nonnull)application didReceiveRemoteNotification:(NSDictionary * _Nonnull)userInfo fetchCompletionHandler:(void (^ _Nonnull)(UIBackgroundFetchResult))completionHandler;
- (BOOL)application:(UIApplication * _Nonnull)application willContinueUserActivityWithType:(NSString * _Nonnull)userActivityType SWIFT_WARN_UNUSED_RESULT;
- (BOOL)application:(UIApplication * _Nonnull)application continueUserActivity:(NSUserActivity * _Nonnull)userActivity restorationHandler:(void (^ _Nonnull)(NSArray<id <UIUserActivityRestoring>> * _Nullable))restorationHandler SWIFT_WARN_UNUSED_RESULT;
- (void)application:(UIApplication * _Nonnull)application didUpdateUserActivity:(NSUserActivity * _Nonnull)userActivity;
- (void)application:(UIApplication * _Nonnull)application didFailToContinueUserActivityWithType:(NSString * _Nonnull)userActivityType error:(NSError * _Nonnull)error;
- (void)application:(UIApplication * _Nonnull)application performActionForShortcutItem:(UIApplicationShortcutItem * _Nonnull)shortcutItem completionHandler:(void (^ _Nonnull)(BOOL))completionHandler;
- (void)application:(UIApplication * _Nonnull)application performFetchWithCompletionHandler:(void (^ _Nonnull)(UIBackgroundFetchResult))completionHandler;
- (BOOL)application:(UIApplication * _Nonnull)app openURL:(NSURL * _Nonnull)url options:(NSDictionary<UIApplicationOpenURLOptionsKey, id> * _Nonnull)options SWIFT_WARN_UNUSED_RESULT;
- (UIInterfaceOrientationMask)application:(UIApplication * _Nonnull)application supportedInterfaceOrientationsForWindow:(UIWindow * _Nullable)window SWIFT_WARN_UNUSED_RESULT;
- (void)customizeRootView:(RCTRootView * _Nonnull)rootView;
+ (void)registerSubscribersFromModulesProvider:(ModulesProvider * _Nonnull)modulesProvider;
+ (void)registerSubscriber:(id <EXAppDelegateSubscriberProtocol> _Nonnull)subscriber;
+ (id <EXAppDelegateSubscriberProtocol> _Nullable)getSubscriber:(NSString * _Nonnull)name SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class UIView;
/// Typealias to <code>UIApplicationDelegate</code> protocol.
/// Might be useful for compatibility reasons if we decide to add more things here.
SWIFT_PROTOCOL_NAMED("ExpoAppDelegateSubscriberProtocol")
@protocol EXAppDelegateSubscriberProtocol <UIApplicationDelegate>
@optional
- (void)customizeRootView:(UIView * _Nonnull)rootView;
@end

@class NSCoder;
SWIFT_CLASS_NAMED("ExpoFabricView")
@interface ExpoFabricView : ExpoFabricViewObjC
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER SWIFT_UNAVAILABLE;
- (void)updateProps:(NSDictionary<NSString *, id> * _Nonnull)props;
/// Calls lifecycle methods registered by <code>OnViewDidUpdateProps</code> definition component.
- (void)viewDidUpdateProps;
/// Returns a bool value whether the view supports prop with the given name.
- (BOOL)supportsPropWithName:(NSString * _Nonnull)name SWIFT_WARN_UNUSED_RESULT;
/// Called by React Native to check if the view supports recycling.
+ (BOOL)shouldBeRecycled SWIFT_WARN_UNUSED_RESULT;
/// Dynamically creates a subclass of the <code>ExpoFabricView</code> class with injected app context and name of the associated module.
/// The new subclass is saved in the registry, so when asked for the next time, it’s returned from cache with the updated app context.
/// note:
/// Apple’s documentation says that classes created with <code>objc_allocateClassPair</code> should then be registered using <code>objc_registerClassPair</code>,
/// but we can’t do that as there might be more than one class with the same name (Expo Go) and allocating another one would return <code>nil</code>.
+ (Class _Nullable)makeViewClassForAppContext:(EXAppContext * _Nonnull)appContext moduleName:(NSString * _Nonnull)moduleName className:(NSString * _Nonnull)className SWIFT_WARN_UNUSED_RESULT;
@end

/// An extensible react instance creation delegate. This class will loop through each <code>ExpoReactDelegateHandler</code> to determine the winner to create the instance.
SWIFT_CLASS_NAMED("ExpoReactDelegate")
@interface EXReactDelegate : NSObject
- (UIView * _Nonnull)createReactRootViewWithModuleName:(NSString * _Nonnull)moduleName initialProperties:(NSDictionary * _Nullable)initialProperties launchOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions SWIFT_WARN_UNUSED_RESULT;
- (NSURL * _Nullable)bundleURL SWIFT_WARN_UNUSED_RESULT;
- (UIViewController * _Nonnull)createRootViewController SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// The handler for <code>ExpoReactDelegate</code>. A module can implement a handler to process react instance creation.
SWIFT_CLASS_NAMED("ExpoReactDelegateHandler")
@interface EXReactDelegateHandler : NSObject
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (UIView * _Nullable)createReactRootViewWithReactDelegate:(EXReactDelegate * _Nonnull)reactDelegate moduleName:(NSString * _Nonnull)moduleName initialProperties:(NSDictionary * _Nullable)initialProperties launchOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions SWIFT_WARN_UNUSED_RESULT;
/// Clients could override this getter to serve the latest bundleURL for React instance.
/// For example, expo-updates uses this to serve the newer bundleURL from <code>Updates.reloadAsync()</code>.
- (NSURL * _Nullable)bundleURLWithReactDelegate:(EXReactDelegate * _Nonnull)reactDelegate SWIFT_WARN_UNUSED_RESULT;
/// If this module wants to handle <code>UIViewController</code> creation for <code>RCTRootView</code>, it can return the instance.
/// Otherwise return nil.
- (UIViewController * _Nullable)createRootViewControllerWithReactDelegate:(EXReactDelegate * _Nonnull)reactDelegate SWIFT_WARN_UNUSED_RESULT;
@end

@protocol EXRequestCdpInterceptorDelegate;
/// The <code>ExpoRequestInterceptorProtocolDelegate</code> implementation to
/// dispatch CDP (Chrome DevTools Protocol: https://chromedevtools.github.io/devtools-protocol/) events.
SWIFT_CLASS_NAMED("ExpoRequestCdpInterceptor")
@interface EXRequestCdpInterceptor : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) EXRequestCdpInterceptor * _Nonnull shared;)
+ (EXRequestCdpInterceptor * _Nonnull)shared SWIFT_WARN_UNUSED_RESULT;
- (void)setDelegate:(id <EXRequestCdpInterceptorDelegate> _Nullable)newValue;
@end

/// The delegate to dispatch CDP events for ExpoRequestCdpInterceptor
SWIFT_PROTOCOL_NAMED("ExpoRequestCdpInterceptorDelegate")
@protocol EXRequestCdpInterceptorDelegate
- (void)dispatch:(NSString * _Nonnull)event;
@end

@class NSURLRequest;
@class NSCachedURLResponse;
@protocol NSURLProtocolClient;
@class NSURLSession;
@class NSURLSessionDataTask;
@class NSURLSessionTask;
@class NSURLResponse;
@class NSHTTPURLResponse;
@class NSURLAuthenticationChallenge;
@class NSURLCredential;
/// A <code>URLSession</code> interceptor which passes network events to its delegate
SWIFT_CLASS_NAMED("ExpoRequestInterceptorProtocol")
@interface EXRequestInterceptorProtocol : NSURLProtocol <NSURLSessionDataDelegate>
+ (BOOL)canInitWithRequest:(NSURLRequest * _Nonnull)request SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithRequest:(NSURLRequest * _Nonnull)request cachedResponse:(NSCachedURLResponse * _Nullable)cachedResponse client:(id <NSURLProtocolClient> _Nullable)client SWIFT_UNAVAILABLE;
+ (NSURLRequest * _Nonnull)canonicalRequestForRequest:(NSURLRequest * _Nonnull)request SWIFT_WARN_UNUSED_RESULT;
- (void)startLoading;
- (void)stopLoading;
- (void)URLSession:(NSURLSession * _Nonnull)_ dataTask:(NSURLSessionDataTask * _Nonnull)_ didReceiveData:(NSData * _Nonnull)data;
- (void)URLSession:(NSURLSession * _Nonnull)_ task:(NSURLSessionTask * _Nonnull)task didCompleteWithError:(NSError * _Nullable)error;
- (void)URLSession:(NSURLSession * _Nonnull)_ dataTask:(NSURLSessionDataTask * _Nonnull)dataTask didReceiveResponse:(NSURLResponse * _Nonnull)response completionHandler:(void (^ _Nonnull)(NSURLSessionResponseDisposition))completionHandler;
- (void)URLSession:(NSURLSession * _Nonnull)_ task:(NSURLSessionTask * _Nonnull)task willPerformHTTPRedirection:(NSHTTPURLResponse * _Nonnull)response newRequest:(NSURLRequest * _Nonnull)request completionHandler:(void (^ _Nonnull)(NSURLRequest * _Nullable))completionHandler;
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task didReceiveChallenge:(NSURLAuthenticationChallenge * _Nonnull)challenge completionHandler:(void (^ _Nonnull)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler;
@end

SWIFT_CLASS_NAMED("ExpoRuntime")
@interface EXRuntime : EXJavaScriptRuntime
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class Protocol;
SWIFT_CLASS_NAMED("FileSystemLegacyUtilities")
@interface EXFileSystemLegacyUtilities : NSObject <EXFilePermissionModuleInterface, EXFileSystemInterface, EXInternalModule>
@property (nonatomic, copy) NSString * _Nonnull documentDirectory;
@property (nonatomic, copy) NSString * _Nonnull cachesDirectory;
@property (nonatomic, copy) NSString * _Nonnull applicationSupportDirectory;
- (nonnull instancetype)initWithDocumentDirectory:(NSString * _Nonnull)documentDirectory cachesDirectory:(NSString * _Nonnull)cachesDirectory applicationSupportDirectory:(NSString * _Nonnull)applicationSupportDirectory OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
+ (NSArray<Protocol *> * _Nonnull)exportedInterfaces SWIFT_WARN_UNUSED_RESULT;
- (EXFileSystemPermissionFlags)permissionsForURI:(NSURL * _Nonnull)uri SWIFT_WARN_UNUSED_RESULT;
- (NSString * _Nonnull)generatePathInDirectory:(NSString * _Nonnull)directory withExtension:(NSString * _Nonnull)ext SWIFT_WARN_UNUSED_RESULT;
- (BOOL)ensureDirExistsWithPath:(NSString * _Nonnull)path;
- (EXFileSystemPermissionFlags)getPathPermissions:(NSString * _Nonnull)path SWIFT_WARN_UNUSED_RESULT;
- (EXFileSystemPermissionFlags)getInternalPathPermissions:(NSURL * _Nonnull)url SWIFT_WARN_UNUSED_RESULT;
- (EXFileSystemPermissionFlags)getExternalPathPermissions:(NSURL * _Nonnull)url SWIFT_WARN_UNUSED_RESULT;
@end

/// The default implementation for modules provider.
/// The proper implementation is generated by autolinking as part of <code>pod install</code> command.
SWIFT_CLASS("_TtC15ExpoModulesCore15ModulesProvider")
@interface ModulesProvider : NSObject
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// Shared URLSessionDelegate instance and delete calls back to ExpoRequestInterceptorProtocol instances.
SWIFT_CLASS("_TtC15ExpoModulesCore30URLSessionSessionDelegateProxy")
@interface URLSessionSessionDelegateProxy : NSObject <NSURLSessionDataDelegate>
- (void)URLSession:(NSURLSession * _Nonnull)session dataTask:(NSURLSessionDataTask * _Nonnull)dataTask didReceiveData:(NSData * _Nonnull)didReceive;
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task didCompleteWithError:(NSError * _Nullable)didCompleteWithError;
- (void)URLSession:(NSURLSession * _Nonnull)session dataTask:(NSURLSessionDataTask * _Nonnull)dataTask didReceiveResponse:(NSURLResponse * _Nonnull)didReceive completionHandler:(void (^ _Nonnull)(NSURLSessionResponseDisposition))completionHandler;
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task willPerformHTTPRedirection:(NSHTTPURLResponse * _Nonnull)willPerformHTTPRedirection newRequest:(NSURLRequest * _Nonnull)newRequest completionHandler:(void (^ _Nonnull)(NSURLRequest * _Nullable))completionHandler;
- (void)URLSession:(NSURLSession * _Nonnull)session task:(NSURLSessionTask * _Nonnull)task didReceiveChallenge:(NSURLAuthenticationChallenge * _Nonnull)challenge completionHandler:(void (^ _Nonnull)(NSURLSessionAuthChallengeDisposition, NSURLCredential * _Nullable))completionHandler;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Each module that has a view manager definition needs to be wrapped by <code>RCTViewManager</code>.
/// Unfortunately, we can’t use just one class because React Native checks for duplicated classes.
/// We’re generating its subclasses in runtime as a workaround.
SWIFT_CLASS("_TtC15ExpoModulesCore17ViewModuleWrapper")
@interface ViewModuleWrapper : RCTViewManager
/// The designated initializer that is used by React Native to create module instances.
/// https://github.com/facebook/react-native/blob/540c41be9/packages/react-native/React/Views/RCTComponentData.m#L506-L507
/// It doesn’t matter to return dummy class here. The wrapper will then to subclass dynamically.
/// Must be called on a dynamic class to get access to underlying wrapped module. Throws fatal exception otherwise.
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// Dummy initializer, for use only in <code>EXModuleRegistryAdapter.extraModulesForModuleRegistry:</code>.
- (nonnull instancetype)initWithDummy:(id _Nullable)dummy OBJC_DESIGNATED_INITIALIZER;
/// Returns the original name of the wrapped module.
- (NSString * _Nonnull)name SWIFT_WARN_UNUSED_RESULT;
/// Static function that returns the class name, but keep in mind that dynamic wrappers
/// have custom class name (see <code>objc_allocateClassPair</code> invocation in <code>createViewModuleWrapperClass</code>).
+ (NSString * _Nonnull)moduleName SWIFT_WARN_UNUSED_RESULT;
/// The view manager wrapper doesn’t require main queue setup — it doesn’t call any UI-related stuff on <code>init</code>.
/// Also, lazy-loaded modules must return false here.
+ (BOOL)requiresMainQueueSetup SWIFT_WARN_UNUSED_RESULT;
/// Creates a view from the wrapped module.
- (UIView * _Null_unspecified)view SWIFT_WARN_UNUSED_RESULT;
/// Creates a subclass of <code>ViewModuleWrapper</code> in runtime. The new class overrides <code>moduleName</code> stub.
+ (SWIFT_METATYPE(ViewModuleWrapper) _Nullable)createViewModuleWrapperClassWithModule:(ViewModuleWrapper * _Nonnull)module_ appId:(NSString * _Nullable)appId SWIFT_WARN_UNUSED_RESULT;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
