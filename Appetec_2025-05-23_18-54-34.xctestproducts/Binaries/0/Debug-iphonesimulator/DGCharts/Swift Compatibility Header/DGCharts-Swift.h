#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 6.1.2 effective-5.10 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)
#ifndef DGCHARTS_SWIFT_H
#define DGCHARTS_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef unsigned char char8_t;
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import CoreFoundation;
@import CoreGraphics;
@import Foundation;
@import ObjectiveC;
@import UIKit;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="DGCharts",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

@class ChartViewPortHandler;
@class ChartTransformer;
@class ChartViewBase;
SWIFT_CLASS_NAMED("ViewPortJob")
@interface ChartViewPortJob : NSObject
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler xValue:(double)xValue yValue:(double)yValue transformer:(ChartTransformer * _Nonnull)transformer view:(ChartViewBase * _Nonnull)view OBJC_DESIGNATED_INITIALIZER;
- (void)doJob;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS("_TtC8DGCharts19AnimatedViewPortJob")
@interface AnimatedViewPortJob : ChartViewPortJob
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler xValue:(double)xValue yValue:(double)yValue transformer:(ChartTransformer * _Nonnull)transformer view:(ChartViewBase * _Nonnull)view xOrigin:(CGFloat)xOrigin yOrigin:(CGFloat)yOrigin duration:(NSTimeInterval)duration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing OBJC_DESIGNATED_INITIALIZER;
- (void)doJob;
- (void)start;
- (void)stopWithFinish:(BOOL)finish;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler xValue:(double)xValue yValue:(double)yValue transformer:(ChartTransformer * _Nonnull)transformer view:(ChartViewBase * _Nonnull)view SWIFT_UNAVAILABLE;
@end

SWIFT_CLASS("_TtC8DGCharts19AnimatedMoveViewJob")
@interface AnimatedMoveViewJob : AnimatedViewPortJob
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler xValue:(double)xValue yValue:(double)yValue transformer:(ChartTransformer * _Nonnull)transformer view:(ChartViewBase * _Nonnull)view xOrigin:(CGFloat)xOrigin yOrigin:(CGFloat)yOrigin duration:(NSTimeInterval)duration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing OBJC_DESIGNATED_INITIALIZER;
@end

@class ChartYAxis;
SWIFT_CLASS("_TtC8DGCharts19AnimatedZoomViewJob")
@interface AnimatedZoomViewJob : AnimatedViewPortJob
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler transformer:(ChartTransformer * _Nonnull)transformer view:(ChartViewBase * _Nonnull)view yAxis:(ChartYAxis * _Nonnull)yAxis xAxisRange:(double)xAxisRange scaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY xOrigin:(CGFloat)xOrigin yOrigin:(CGFloat)yOrigin zoomCenterX:(CGFloat)zoomCenterX zoomCenterY:(CGFloat)zoomCenterY zoomOriginX:(CGFloat)zoomOriginX zoomOriginY:(CGFloat)zoomOriginY duration:(NSTimeInterval)duration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler xValue:(double)xValue yValue:(double)yValue transformer:(ChartTransformer * _Nonnull)transformer view:(ChartViewBase * _Nonnull)view xOrigin:(CGFloat)xOrigin yOrigin:(CGFloat)yOrigin duration:(NSTimeInterval)duration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing SWIFT_UNAVAILABLE;
@end

@protocol ChartAnimatorDelegate;
enum ChartEasingOption : NSInteger;
SWIFT_CLASS_NAMED("Animator")
@interface ChartAnimator : NSObject
@property (nonatomic, weak) id <ChartAnimatorDelegate> _Nullable delegate;
@property (nonatomic, copy) void (^ _Nullable updateBlock)(void);
@property (nonatomic, copy) void (^ _Nullable stopBlock)(void);
/// the phase that is animated and influences the drawn values on the x-axis
@property (nonatomic) double phaseX;
/// the phase that is animated and influences the drawn values on the y-axis
@property (nonatomic) double phaseY;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (void)stop;
/// Animates the drawing / rendering of the chart on both x- and y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param yAxisDuration duration for animating the y axis
///
/// \param easingX an easing function for the animation on the x axis
///
/// \param easingY an easing function for the animation on the y axis
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration yAxisDuration:(NSTimeInterval)yAxisDuration easingX:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easingX easingY:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easingY;
/// Animates the drawing / rendering of the chart on both x- and y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param yAxisDuration duration for animating the y axis
///
/// \param easingOptionX the easing function for the animation on the x axis
///
/// \param easingOptionY the easing function for the animation on the y axis
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration yAxisDuration:(NSTimeInterval)yAxisDuration easingOptionX:(enum ChartEasingOption)easingOptionX easingOptionY:(enum ChartEasingOption)easingOptionY;
/// Animates the drawing / rendering of the chart on both x- and y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param yAxisDuration duration for animating the y axis
///
/// \param easing an easing function for the animation
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration yAxisDuration:(NSTimeInterval)yAxisDuration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing;
/// Animates the drawing / rendering of the chart on both x- and y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param yAxisDuration duration for animating the y axis
///
/// \param easingOption the easing function for the animation
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration yAxisDuration:(NSTimeInterval)yAxisDuration easingOption:(enum ChartEasingOption)easingOption;
/// Animates the drawing / rendering of the chart the x-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param easing an easing function for the animation
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing;
/// Animates the drawing / rendering of the chart the x-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param easingOption the easing function for the animation
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration easingOption:(enum ChartEasingOption)easingOption;
/// Animates the drawing / rendering of the chart the y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param yAxisDuration duration for animating the y axis
///
/// \param easing an easing function for the animation
///
- (void)animateWithYAxisDuration:(NSTimeInterval)yAxisDuration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing;
/// Animates the drawing / rendering of the chart the y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param yAxisDuration duration for animating the y axis
///
/// \param easingOption the easing function for the animation
///
- (void)animateWithYAxisDuration:(NSTimeInterval)yAxisDuration easingOption:(enum ChartEasingOption)easingOption;
@end

SWIFT_PROTOCOL_NAMED("AnimatorDelegate")
@protocol ChartAnimatorDelegate
/// Called when the Animator has stepped.
- (void)animatorUpdated:(ChartAnimator * _Nonnull)animator;
/// Called when the Animator has stopped.
- (void)animatorStopped:(ChartAnimator * _Nonnull)animator;
@end

/// This class encapsulates everything both Axis, Legend and LimitLines have in common
SWIFT_CLASS_NAMED("ComponentBase")
@interface ChartComponentBase : NSObject
/// flag that indicates if this component is enabled or not
@property (nonatomic) BOOL enabled;
/// The offset this component has on the x-axis
/// <em>default</em>: 5.0
@property (nonatomic) CGFloat xOffset;
/// The offset this component has on the y-axis
/// <em>default</em>: 5.0 (or 0.0 on ChartYAxis)
@property (nonatomic) CGFloat yOffset;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, readonly) BOOL isEnabled;
@end

@class UIFont;
@class UIColor;
@class NSString;
@protocol ChartAxisValueFormatter;
@class ChartLimitLine;
/// Base class for all axes
SWIFT_CLASS_NAMED("AxisBase")
@interface ChartAxisBase : ChartComponentBase
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, strong) UIFont * _Nonnull labelFont;
@property (nonatomic, strong) UIColor * _Nonnull labelTextColor;
@property (nonatomic, strong) UIColor * _Nonnull axisLineColor;
@property (nonatomic) CGFloat axisLineWidth;
@property (nonatomic) CGFloat axisLineDashPhase;
@property (nonatomic, copy) NSArray<NSNumber *> * _Null_unspecified axisLineDashLengths;
@property (nonatomic, strong) UIColor * _Nonnull gridColor;
@property (nonatomic) CGFloat gridLineWidth;
@property (nonatomic) CGFloat gridLineDashPhase;
@property (nonatomic, copy) NSArray<NSNumber *> * _Null_unspecified gridLineDashLengths;
@property (nonatomic) CGLineCap gridLineCap;
@property (nonatomic) BOOL drawGridLinesEnabled;
@property (nonatomic) BOOL drawAxisLineEnabled;
/// flag that indicates of the labels of this axis should be drawn or not
@property (nonatomic) BOOL drawLabelsEnabled;
/// Centers the axis labels instead of drawing them at their original position.
/// This is useful especially for grouped BarChart.
@property (nonatomic) BOOL centerAxisLabelsEnabled;
@property (nonatomic, readonly) BOOL isCenterAxisLabelsEnabled;
/// Are the LimitLines drawn behind the data or in front of the data?
/// <em>default</em>: false
@property (nonatomic) BOOL drawLimitLinesBehindDataEnabled;
/// Are the grid lines drawn behind the data or in front of the data?
/// <em>default</em>: true
@property (nonatomic) BOOL drawGridLinesBehindDataEnabled;
/// the flag can be used to turn off the antialias for grid lines
@property (nonatomic) BOOL gridAntialiasEnabled;
/// the actual array of entries
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull entries;
/// axis label entries only used for centered labels
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull centeredEntries;
/// the number of entries the legend contains
@property (nonatomic, readonly) NSInteger entryCount;
/// the number of decimal digits to use (for the default formatter
@property (nonatomic) NSInteger decimals;
/// When true, axis labels are controlled by the <code>granularity</code> property.
/// When false, axis values could possibly be repeated.
/// This could happen if two adjacent axis values are rounded to same value.
/// If using granularity this could be avoided by having fewer axis values visible.
@property (nonatomic) BOOL granularityEnabled;
/// The minimum interval between axis values.
/// This can be used to avoid label duplicating when zooming in.
/// <em>default</em>: 1.0
@property (nonatomic) double granularity;
/// The minimum interval between axis values.
@property (nonatomic, readonly) BOOL isGranularityEnabled;
/// if true, the set number of y-labels will be forced
@property (nonatomic) BOOL forceLabelsEnabled;
- (NSString * _Nonnull)getLongestLabel SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The formatted label at the specified index. This will either use the auto-formatter or the custom formatter (if one is set).
- (NSString * _Nonnull)getFormattedLabel:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
/// Sets the formatter to be used for formatting the axis labels.
/// If no formatter is set, the chart will automatically determine a reasonable formatting (concerning decimals) for all the values that are drawn inside the chart.
/// Use <code>nil</code> to use the formatter calculated by the chart.
@property (nonatomic, strong) id <ChartAxisValueFormatter> _Nullable valueFormatter;
@property (nonatomic, readonly) BOOL isDrawGridLinesEnabled;
@property (nonatomic, readonly) BOOL isDrawAxisLineEnabled;
@property (nonatomic, readonly) BOOL isDrawLabelsEnabled;
/// Are the LimitLines drawn behind the data or in front of the data?
/// <em>default</em>: false
@property (nonatomic, readonly) BOOL isDrawLimitLinesBehindDataEnabled;
/// Are the grid lines drawn behind the data or in front of the data?
/// <em>default</em>: true
@property (nonatomic, readonly) BOOL isDrawGridLinesBehindDataEnabled;
/// Extra spacing for <code>axisMinimum</code> to be added to automatically calculated <code>axisMinimum</code>
@property (nonatomic) double spaceMin;
/// Extra spacing for <code>axisMaximum</code> to be added to automatically calculated <code>axisMaximum</code>
@property (nonatomic) double spaceMax;
/// the total range of values this axis covers
@property (nonatomic) double axisRange;
/// The minumum number of labels on the axis
@property (nonatomic) NSInteger axisMinLabels;
/// The maximum number of labels on the axis
@property (nonatomic) NSInteger axisMaxLabels;
/// the number of label entries the axis should have
/// max = 25,
/// min = 2,
/// default = 6,
/// be aware that this number is not fixed and can only be approximated
@property (nonatomic) NSInteger labelCount;
- (void)setLabelCount:(NSInteger)count force:(BOOL)force;
/// <code>true</code> if focing the y-label count is enabled. Default: false
@property (nonatomic, readonly) BOOL isForceLabelsEnabled;
/// Adds a new ChartLimitLine to this axis.
- (void)addLimitLine:(ChartLimitLine * _Nonnull)line;
/// Removes the specified ChartLimitLine from the axis.
- (void)removeLimitLine:(ChartLimitLine * _Nonnull)line;
/// Removes all LimitLines from the axis.
- (void)removeAllLimitLines;
/// The LimitLines of this axis.
@property (nonatomic, readonly, copy) NSArray<ChartLimitLine *> * _Nonnull limitLines;
/// By calling this method, any custom minimum value that has been previously set is reseted, and the calculation is done automatically.
- (void)resetCustomAxisMin;
@property (nonatomic, readonly) BOOL isAxisMinCustom;
/// By calling this method, any custom maximum value that has been previously set is reseted, and the calculation is done automatically.
- (void)resetCustomAxisMax;
@property (nonatomic, readonly) BOOL isAxisMaxCustom;
/// The minimum value for this axis.
/// If set, this value will not be calculated automatically depending on the provided data.
/// Use <code>resetCustomAxisMin()</code> to undo this.
@property (nonatomic) double axisMinimum;
/// The maximum value for this axis.
/// If set, this value will not be calculated automatically depending on the provided data.
/// Use <code>resetCustomAxisMax()</code> to undo this.
@property (nonatomic) double axisMaximum;
/// Calculates the minimum, maximum and range values of the YAxis with the given minimum and maximum values from the chart data.
/// \param dataMin the y-min value according to chart data
///
/// \param dataMax the y-max value according to chart
///
- (void)calculateWithMin:(double)dataMin max:(double)dataMax;
@end

/// An interface for providing custom axis Strings.
SWIFT_PROTOCOL_NAMED("AxisValueFormatter")
@protocol ChartAxisValueFormatter
/// Called when a value from an axis is formatted before being drawn.
/// For performance reasons, avoid excessive calculations and memory allocations inside this method.
/// \param value the value that is currently being drawn
///
/// \param axis the axis that the value belongs to
///
///
/// returns:
/// The customized label that is drawn on the x-axis.
- (NSString * _Nonnull)stringForValue:(double)value axis:(ChartAxisBase * _Nullable)axis SWIFT_WARN_UNUSED_RESULT;
@end

@protocol ChartDataSetProtocol;
@class ChartDataEntry;
enum AxisDependency : NSInteger;
@class ChartHighlight;
@protocol ChartValueFormatter;
SWIFT_CLASS("_TtC8DGCharts9ChartData")
@interface ChartData : NSObject
@property (nonatomic, readonly) double xMax;
@property (nonatomic, readonly) double xMin;
@property (nonatomic, readonly) double yMax;
@property (nonatomic, readonly) double yMin;
/// When the data entry labels are generated identifiers, set this property to prepend a string before each identifier
/// For example, if a label is “#3”, settings this property to “Item” allows it to be spoken as “Item #3”
@property (nonatomic, copy) NSString * _Nullable accessibilityEntryLabelPrefix;
/// When the data entry value requires a unit, use this property to append the string representation of the unit to the value
/// For example, if a value is “44.1”, setting this property to “m” allows it to be spoken as “44.1 m”
@property (nonatomic, copy) NSString * _Nullable accessibilityEntryLabelSuffix;
/// If the data entry value is a count, set this to true to allow plurals and other grammatical changes
/// <em>default</em>: false
@property (nonatomic) BOOL accessibilityEntryLabelSuffixIsCount;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSet:(id <ChartDataSetProtocol> _Nonnull)dataSet;
/// Call this method to let the ChartData know that the underlying data has changed.
/// Calling this performs all necessary recalculations needed when the contained data has changed.
- (void)notifyDataChanged;
- (void)calcMinMaxYFromX:(double)fromX toX:(double)toX;
/// calc minimum and maximum y value over all datasets
- (void)calcMinMax;
/// Adjusts the current minimum and maximum values based on the provided Entry object.
- (void)calcMinMaxWithEntry:(ChartDataEntry * _Nonnull)e axis:(enum AxisDependency)axis;
/// Adjusts the minimum and maximum values based on the given DataSet.
- (void)calcMinMaxWithDataSet:(id <ChartDataSetProtocol> _Nonnull)d;
/// The number of LineDataSets this object contains
@property (nonatomic, readonly) NSInteger dataSetCount;
- (double)getYMinWithAxis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
- (double)getYMaxWithAxis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
/// All DataSet objects this ChartData object holds.
@property (nonatomic, copy) NSArray<id <ChartDataSetProtocol>> * _Nonnull dataSets;
/// Get the Entry for a corresponding highlight object
/// \param highlight 
///
///
/// returns:
/// The entry that is highlighted
- (ChartDataEntry * _Nullable)entryFor:(ChartHighlight * _Nonnull)highlight SWIFT_WARN_UNUSED_RESULT;
/// <em>IMPORTANT: This method does calculations at runtime. Use with care in performance critical situations.</em>
/// \param label 
///
/// \param ignorecase 
///
///
/// returns:
/// The DataSet Object with the given label. Sensitive or not.
- (id <ChartDataSetProtocol> _Nullable)dataSetForLabel:(NSString * _Nonnull)label ignorecase:(BOOL)ignorecase SWIFT_WARN_UNUSED_RESULT;
- (id <ChartDataSetProtocol> _Nullable)dataSetAtIndex:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
/// Removes the given DataSet from this data object.
/// Also recalculates all minimum and maximum values.
///
/// returns:
/// <code>true</code> if a DataSet was removed, <code>false</code> ifno DataSet could be removed.
- (id <ChartDataSetProtocol> _Nullable)removeDataSet:(id <ChartDataSetProtocol> _Nonnull)dataSet;
/// Adds an Entry to the DataSet at the specified index. Entries are added to the end of the list.
- (void)addEntry:(ChartDataEntry * _Nonnull)e dataSetIndex:(NSInteger)dataSetIndex;
/// Removes the given Entry object from the DataSet at the specified index.
- (BOOL)removeEntry:(ChartDataEntry * _Nonnull)entry dataSetIndex:(NSInteger)dataSetIndex;
/// Removes the Entry object closest to the given xIndex from the ChartDataSet at the
/// specified index.
///
/// returns:
/// <code>true</code> if an entry was removed, <code>false</code> ifno Entry was found that meets the specified requirements.
- (BOOL)removeEntryWithXValue:(double)xValue dataSetIndex:(NSInteger)dataSetIndex;
///
/// returns:
/// The DataSet that contains the provided Entry, or null, if no DataSet contains this entry.
- (id <ChartDataSetProtocol> _Nullable)getDataSetForEntry:(ChartDataEntry * _Nonnull)e SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The index of the provided DataSet in the DataSet array of this data object, or -1 if it does not exist.
- (NSInteger)indexOf:(id <ChartDataSetProtocol> _Nonnull)dataSet SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The first DataSet from the datasets-array that has it’s dependency on the left axis. Returns null if no DataSet with left dependency could be found.
- (id <ChartDataSetProtocol> _Nullable)getFirstLeftWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The first DataSet from the datasets-array that has it’s dependency on the right axis. Returns null if no DataSet with right dependency could be found.
- (id <ChartDataSetProtocol> _Nullable)getFirstRightWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// All colors used across all DataSet objects this object represents.
@property (nonatomic, readonly, copy) NSArray<UIColor *> * _Nonnull colors;
/// Sets a custom ValueFormatter for all DataSets this data object contains.
- (void)setValueFormatter:(id <ChartValueFormatter> _Nonnull)formatter;
/// Sets the color of the value-text (color in which the value-labels are drawn) for all DataSets this data object contains.
- (void)setValueTextColor:(UIColor * _Nonnull)color;
/// Sets the font for all value-labels for all DataSets this data object contains.
- (void)setValueFont:(UIFont * _Nonnull)font;
/// Enables / disables drawing values (value-text) for all DataSets this data object contains.
- (void)setDrawValues:(BOOL)enabled;
/// Enables / disables highlighting values for all DataSets this data object contains.
/// If set to true, this means that values can be highlighted programmatically or by touch gesture.
@property (nonatomic) BOOL isHighlightEnabled;
/// Clears this data object from all DataSets and removes all Entries.
/// Don’t forget to invalidate the chart after this.
- (void)clearValues;
/// Checks if this data object contains the specified DataSet.
///
/// returns:
/// <code>true</code> if so, <code>false</code> ifnot.
- (BOOL)containsWithDataSet:(id <ChartDataSetProtocol> _Nonnull)dataSet SWIFT_WARN_UNUSED_RESULT;
/// The total entry count across all DataSet objects this data object contains.
@property (nonatomic, readonly) NSInteger entryCount;
/// The DataSet object with the maximum number of entries or null if there are no DataSets.
@property (nonatomic, readonly, strong) id <ChartDataSetProtocol> _Nullable maxEntryCountSet;
@end

SWIFT_CLASS("_TtC8DGCharts35BarLineScatterCandleBubbleChartData")
@interface BarLineScatterCandleBubbleChartData : ChartData
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts12BarChartData")
@interface BarChartData : BarLineScatterCandleBubbleChartData
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets OBJC_DESIGNATED_INITIALIZER;
/// The width of the bars on the x-axis, in values (not pixels)
/// <em>default</em>: 0.85
@property (nonatomic) double barWidth;
/// Groups all BarDataSet objects this data object holds together by modifying the x-value of their entries.
/// Previously set x-values of entries will be overwritten. Leaves space between bars and groups as specified by the parameters.
/// Do not forget to call notifyDataSetChanged() on your BarChart object after calling this method.
/// \param fromX the starting point on the x-axis where the grouping should begin
///
/// \param groupSpace The space between groups of bars in values (not pixels) e.g. 0.8f for bar width 1f
///
/// \param barSpace The space between individual bars in values (not pixels) e.g. 0.1f for bar width 1f
///
- (void)groupBarsFromX:(double)fromX groupSpace:(double)groupSpace barSpace:(double)barSpace;
/// In case of grouped bars, this method returns the space an individual group of bar needs on the x-axis.
/// \param groupSpace 
///
/// \param barSpace 
///
- (double)groupWidthWithGroupSpace:(double)groupSpace barSpace:(double)barSpace SWIFT_WARN_UNUSED_RESULT;
@end

@class UIImage;
SWIFT_CLASS("_TtC8DGCharts18ChartDataEntryBase")
@interface ChartDataEntryBase : NSObject
/// the y value
@property (nonatomic) double y;
/// optional spot for additional data this Entry represents
@property (nonatomic) id _Nullable data;
/// optional icon image
@property (nonatomic, strong) UIImage * _Nullable icon;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// An Entry represents one single entry in the chart.
/// \param y the y value (the actual value of the entry)
///
- (nonnull instancetype)initWithY:(double)y OBJC_DESIGNATED_INITIALIZER;
/// \param y the y value (the actual value of the entry)
///
/// \param data Space for additional data this Entry represents.
///
- (nonnull instancetype)initWithY:(double)y data:(id _Nullable)data;
/// \param y the y value (the actual value of the entry)
///
/// \param icon icon image
///
- (nonnull instancetype)initWithY:(double)y icon:(UIImage * _Nullable)icon;
/// \param y the y value (the actual value of the entry)
///
/// \param icon icon image
///
/// \param data Space for additional data this Entry represents.
///
- (nonnull instancetype)initWithY:(double)y icon:(UIImage * _Nullable)icon data:(id _Nullable)data;
@property (nonatomic, readonly, copy) NSString * _Nonnull description;
@end

SWIFT_CLASS("_TtC8DGCharts14ChartDataEntry")
@interface ChartDataEntry : ChartDataEntryBase <NSCopying>
/// the x value
@property (nonatomic) double x;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// An Entry represents one single entry in the chart.
/// \param x the x value
///
/// \param y the y value (the actual value of the entry)
///
- (nonnull instancetype)initWithX:(double)x y:(double)y OBJC_DESIGNATED_INITIALIZER;
/// An Entry represents one single entry in the chart.
/// \param x the x value
///
/// \param y the y value (the actual value of the entry)
///
/// \param data Space for additional data this Entry represents.
///
- (nonnull instancetype)initWithX:(double)x y:(double)y data:(id _Nullable)data;
/// An Entry represents one single entry in the chart.
/// \param x the x value
///
/// \param y the y value (the actual value of the entry)
///
/// \param icon icon image
///
- (nonnull instancetype)initWithX:(double)x y:(double)y icon:(UIImage * _Nullable)icon;
/// An Entry represents one single entry in the chart.
/// \param x the x value
///
/// \param y the y value (the actual value of the entry)
///
/// \param icon icon image
///
/// \param data Space for additional data this Entry represents.
///
- (nonnull instancetype)initWithX:(double)x y:(double)y icon:(UIImage * _Nullable)icon data:(id _Nullable)data;
@property (nonatomic, readonly, copy) NSString * _Nonnull description;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithY:(double)y SWIFT_UNAVAILABLE;
@end

@class ChartRange;
SWIFT_CLASS("_TtC8DGCharts17BarChartDataEntry")
@interface BarChartDataEntry : ChartDataEntry
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// Constructor for normal bars (not stacked).
- (nonnull instancetype)initWithX:(double)x y:(double)y OBJC_DESIGNATED_INITIALIZER;
/// Constructor for normal bars (not stacked).
- (nonnull instancetype)initWithX:(double)x y:(double)y data:(id _Nullable)data;
/// Constructor for normal bars (not stacked).
- (nonnull instancetype)initWithX:(double)x y:(double)y icon:(UIImage * _Nullable)icon;
/// Constructor for normal bars (not stacked).
- (nonnull instancetype)initWithX:(double)x y:(double)y icon:(UIImage * _Nullable)icon data:(id _Nullable)data;
/// Constructor for stacked bar entries.
- (nonnull instancetype)initWithX:(double)x yValues:(NSArray<NSNumber *> * _Nonnull)yValues OBJC_DESIGNATED_INITIALIZER;
/// Constructor for stacked bar entries. One data object for whole stack
- (nonnull instancetype)initWithX:(double)x yValues:(NSArray<NSNumber *> * _Nonnull)yValues icon:(UIImage * _Nullable)icon;
/// Constructor for stacked bar entries. One data object for whole stack
- (nonnull instancetype)initWithX:(double)x yValues:(NSArray<NSNumber *> * _Nonnull)yValues data:(id _Nullable)data;
/// Constructor for stacked bar entries. One data object for whole stack
- (nonnull instancetype)initWithX:(double)x yValues:(NSArray<NSNumber *> * _Nonnull)yValues icon:(UIImage * _Nullable)icon data:(id _Nullable)data;
- (double)sumBelowStackIndex:(NSInteger)stackIndex SWIFT_WARN_UNUSED_RESULT;
/// The sum of all negative values this entry (if stacked) contains. (this is a positive number)
@property (nonatomic, readonly) double negativeSum;
/// The sum of all positive values this entry (if stacked) contains.
@property (nonatomic, readonly) double positiveSum;
- (void)calcPosNegSum;
/// Splits up the stack-values of the given bar-entry into Range objects.
/// \param entry 
///
///
/// returns:
///
- (void)calcRanges;
/// the values the stacked barchart holds
@property (nonatomic, readonly) BOOL isStacked;
/// the values the stacked barchart holds
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable yValues;
/// The ranges of the individual stack-entries. Will return null if this entry is not stacked.
@property (nonatomic, readonly, copy) NSArray<ChartRange *> * _Nullable ranges;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_PROTOCOL("_TtP8DGCharts17ChartDataProvider_")
@protocol ChartDataProvider
/// The minimum x-value of the chart, regardless of zoom or translation.
@property (nonatomic, readonly) double chartXMin;
/// The maximum x-value of the chart, regardless of zoom or translation.
@property (nonatomic, readonly) double chartXMax;
/// The minimum y-value of the chart, regardless of zoom or translation.
@property (nonatomic, readonly) double chartYMin;
/// The maximum y-value of the chart, regardless of zoom or translation.
@property (nonatomic, readonly) double chartYMax;
@property (nonatomic, readonly) CGFloat maxHighlightDistance;
@property (nonatomic, readonly) double xRange;
@property (nonatomic, readonly) CGPoint centerOffsets;
@property (nonatomic, readonly, strong) ChartData * _Nullable data;
@property (nonatomic, readonly) NSInteger maxVisibleCount;
@end

SWIFT_PROTOCOL("_TtP8DGCharts43BarLineScatterCandleBubbleChartDataProvider_")
@protocol BarLineScatterCandleBubbleChartDataProvider <ChartDataProvider>
- (ChartTransformer * _Nonnull)getTransformerForAxis:(enum AxisDependency)forAxis SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isInvertedWithAxis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, readonly) double lowestVisibleX;
@property (nonatomic, readonly) double highestVisibleX;
@end

SWIFT_PROTOCOL("_TtP8DGCharts20BarChartDataProvider_")
@protocol BarChartDataProvider <BarLineScatterCandleBubbleChartDataProvider>
@property (nonatomic, readonly, strong) BarChartData * _Nullable barData;
@property (nonatomic, readonly) BOOL isDrawBarShadowEnabled;
@property (nonatomic, readonly) BOOL isDrawValueAboveBarEnabled;
@property (nonatomic, readonly) BOOL isHighlightFullBarEnabled;
@end

enum ChartDataSetRounding : NSInteger;
enum ChartLegendForm : NSInteger;
SWIFT_PROTOCOL("_TtP8DGCharts20ChartDataSetProtocol_")
@protocol ChartDataSetProtocol
/// Use this method to tell the data set that the underlying data has changed
- (void)notifyDataSetChanged;
/// Calculates the minimum and maximum x and y values (xMin, xMax, yMin, yMax).
- (void)calcMinMax;
/// Calculates the min and max y-values from the Entry closest to the given fromX to the Entry closest to the given toX value.
/// This is only needed for the autoScaleMinMax feature.
- (void)calcMinMaxYFromX:(double)fromX toX:(double)toX;
/// The minimum y-value this DataSet holds
@property (nonatomic, readonly) double yMin;
/// The maximum y-value this DataSet holds
@property (nonatomic, readonly) double yMax;
/// The minimum x-value this DataSet holds
@property (nonatomic, readonly) double xMin;
/// The maximum x-value this DataSet holds
@property (nonatomic, readonly) double xMax;
/// The number of y-values this DataSet represents
@property (nonatomic, readonly) NSInteger entryCount;
///
/// throws:
/// out of bounds
/// if <code>i</code> is out of bounds, it may throw an out-of-bounds exception
///
/// returns:
/// The entry object found at the given index (not x-value!)
- (ChartDataEntry * _Nullable)entryForIndex:(NSInteger)i SWIFT_WARN_UNUSED_RESULT;
/// \param xValue the x-value
///
/// \param closestToY If there are multiple y-values for the specified x-value,
///
/// \param rounding determine whether to round up/down/closest if there is no Entry matching the provided x-value
///
///
/// returns:
/// The first Entry object found at the given x-value with binary search.
/// If the no Entry at the specified x-value is found, this method returns the Entry at the closest x-value according to the rounding.
/// nil if no Entry object at that x-value.
- (ChartDataEntry * _Nullable)entryForXValue:(double)xValue closestToY:(double)yValue rounding:(enum ChartDataSetRounding)rounding SWIFT_WARN_UNUSED_RESULT;
/// \param xValue the x-value
///
/// \param closestToY If there are multiple y-values for the specified x-value,
///
///
/// returns:
/// The first Entry object found at the given x-value with binary search.
/// If the no Entry at the specified x-value is found, this method returns the Entry at the closest x-value.
/// nil if no Entry object at that x-value.
- (ChartDataEntry * _Nullable)entryForXValue:(double)xValue closestToY:(double)yValue SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// All Entry objects found at the given x-value with binary search.
/// An empty array if no Entry object at that x-value.
- (NSArray<ChartDataEntry *> * _Nonnull)entriesForXValue:(double)xValue SWIFT_WARN_UNUSED_RESULT;
/// \param xValue x-value of the entry to search for
///
/// \param closestToY If there are multiple y-values for the specified x-value,
///
/// \param rounding Rounding method if exact value was not found
///
///
/// returns:
/// The array-index of the specified entry.
/// If the no Entry at the specified x-value is found, this method returns the index of the Entry at the closest x-value according to the rounding.
- (NSInteger)entryIndexWithX:(double)xValue closestToY:(double)yValue rounding:(enum ChartDataSetRounding)rounding SWIFT_WARN_UNUSED_RESULT;
/// \param e the entry to search for
///
///
/// returns:
/// The array-index of the specified entry
- (NSInteger)entryIndexWithEntry:(ChartDataEntry * _Nonnull)e SWIFT_WARN_UNUSED_RESULT;
/// Adds an Entry to the DataSet dynamically.
/// <em>optional feature, can return <code>false</code> ifnot implemented</em>
/// Entries are added to the end of the list.
/// \param e the entry to add
///
///
/// returns:
/// <code>true</code> if the entry was added successfully, <code>false</code> ifthis feature is not supported
- (BOOL)addEntry:(ChartDataEntry * _Nonnull)e SWIFT_WARN_UNUSED_RESULT;
/// Adds an Entry to the DataSet dynamically.
/// Entries are added to their appropriate index in the values array respective to their x-position.
/// This will also recalculate the current minimum and maximum values of the DataSet and the value-sum.
/// <em>optional feature, can return <code>false</code> ifnot implemented</em>
/// Entries are added to the end of the list.
/// \param e the entry to add
///
///
/// returns:
/// <code>true</code> if the entry was added successfully, <code>false</code> ifthis feature is not supported
- (BOOL)addEntryOrdered:(ChartDataEntry * _Nonnull)e SWIFT_WARN_UNUSED_RESULT;
/// Removes an Entry from the DataSet dynamically.
/// <em>optional feature, can return <code>false</code> ifnot implemented</em>
/// \param entry the entry to remove
///
///
/// returns:
/// <code>true</code> if the entry was removed successfully, <code>false</code> ifthe entry does not exist or if this feature is not supported
- (BOOL)removeEntry:(ChartDataEntry * _Nonnull)entry SWIFT_WARN_UNUSED_RESULT;
/// Removes the Entry object at the given index in the values array from the DataSet.
/// <em>optional feature, can return <code>false</code> ifnot implemented</em>
/// \param index the index of the entry to remove
///
///
/// returns:
/// <code>true</code> if the entry was removed successfully, <code>false</code> ifthe entry does not exist or if this feature is not supported
- (BOOL)removeEntryWithIndex:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
/// Removes the Entry object closest to the given x-value from the DataSet.
/// <em>optional feature, can return <code>false</code> ifnot implemented</em>
/// \param x the x-value to remove
///
///
/// returns:
/// <code>true</code> if the entry was removed successfully, <code>false</code> ifthe entry does not exist or if this feature is not supported
- (BOOL)removeEntryWithX:(double)x SWIFT_WARN_UNUSED_RESULT;
/// Removes the first Entry (at index 0) of this DataSet from the entries array.
/// <em>optional feature, can return <code>false</code> ifnot implemented</em>
///
/// returns:
/// <code>true</code> if the entry was removed successfully, <code>false</code> ifthe entry does not exist or if this feature is not supported
- (BOOL)removeFirst SWIFT_WARN_UNUSED_RESULT;
/// Removes the last Entry (at index 0) of this DataSet from the entries array.
/// <em>optional feature, can return <code>false</code> ifnot implemented</em>
///
/// returns:
/// <code>true</code> if the entry was removed successfully, <code>false</code> ifthe entry does not exist or if this feature is not supported
- (BOOL)removeLast SWIFT_WARN_UNUSED_RESULT;
/// Checks if this DataSet contains the specified Entry.
///
/// returns:
/// <code>true</code> if contains the entry, <code>false</code> ifnot.
- (BOOL)contains:(ChartDataEntry * _Nonnull)e SWIFT_WARN_UNUSED_RESULT;
/// Removes all values from this DataSet and does all necessary recalculations.
/// <em>optional feature, could throw if not implemented</em>
- (void)clear;
/// The label string that describes the DataSet.
@property (nonatomic, readonly, copy) NSString * _Nullable label;
/// The axis this DataSet should be plotted against.
@property (nonatomic, readonly) enum AxisDependency axisDependency;
/// List representing all colors that are used for drawing the actual values for this DataSet
@property (nonatomic, readonly, copy) NSArray<UIColor *> * _Nonnull valueColors;
/// All the colors that are used for this DataSet.
/// Colors are reused as soon as the number of Entries the DataSet represents is higher than the size of the colors array.
@property (nonatomic, readonly, copy) NSArray<UIColor *> * _Nonnull colors;
///
/// returns:
/// The color at the given index of the DataSet’s color array.
/// This prevents out-of-bounds by performing a modulus on the color index, so colours will repeat themselves.
- (UIColor * _Nonnull)colorAtIndex:(NSInteger)atIndex SWIFT_WARN_UNUSED_RESULT;
- (void)resetColors;
- (void)addColor:(UIColor * _Nonnull)color;
- (void)setColor:(UIColor * _Nonnull)color;
/// if true, value highlighting is enabled
@property (nonatomic) BOOL highlightEnabled;
/// <code>true</code> if value highlighting is enabled for this dataset
@property (nonatomic, readonly) BOOL isHighlightEnabled;
/// Custom formatter that is used instead of the auto-formatter if set
@property (nonatomic, strong) id <ChartValueFormatter> _Nonnull valueFormatter;
/// Sets/get a single color for value text.
/// Setting the color clears the colors array and adds a single color.
/// Getting will return the first color in the array.
@property (nonatomic, strong) UIColor * _Nonnull valueTextColor;
///
/// returns:
/// The color at the specified index that is used for drawing the values inside the chart. Uses modulus internally.
- (UIColor * _Nonnull)valueTextColorAt:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
/// the font for the value-text labels
@property (nonatomic, strong) UIFont * _Nonnull valueFont;
/// The rotation angle (in degrees) for value-text labels
@property (nonatomic) CGFloat valueLabelAngle;
/// The form to draw for this dataset in the legend.
/// Return <code>.Default</code> to use the default legend form.
@property (nonatomic, readonly) enum ChartLegendForm form;
/// The form size to draw for this dataset in the legend.
/// Return <code>NaN</code> to use the default legend form size.
@property (nonatomic, readonly) CGFloat formSize;
/// The line width for drawing the form of this dataset in the legend
/// Return <code>NaN</code> to use the default legend form line width.
@property (nonatomic, readonly) CGFloat formLineWidth;
/// Line dash configuration for legend shapes that consist of lines.
/// This is how much (in pixels) into the dash pattern are we starting from.
@property (nonatomic, readonly) CGFloat formLineDashPhase;
/// Line dash configuration for legend shapes that consist of lines.
/// This is the actual dash pattern.
/// I.e. [2, 3] will paint [–   –   ]
/// [1, 3, 4, 2] will paint [-   ––  -   ––  ]
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nullable formLineDashLengths;
/// Set this to true to draw y-values on the chart.
/// note:
/// For bar and line charts: if <code>maxVisibleCount</code> is reached, no values will be drawn even if this is enabled.
@property (nonatomic) BOOL drawValuesEnabled;
/// <code>true</code> if y-value drawing is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawValuesEnabled;
/// Set this to true to draw y-icons on the chart
/// note:
/// For bar and line charts: if <code>maxVisibleCount</code> is reached, no icons will be drawn even if this is enabled.
@property (nonatomic) BOOL drawIconsEnabled;
/// Returns true if y-icon drawing is enabled, false if not
@property (nonatomic, readonly) BOOL isDrawIconsEnabled;
/// Offset of icons drawn on the chart.
/// For all charts except Pie and Radar it will be ordinary (x offset, y offset).
/// For Pie and Radar chart it will be (y offset, distance from center offset); so if you want icon to be rendered under value, you should increase X component of CGPoint, and if you want icon to be rendered closet to center, you should decrease height component of CGPoint.
@property (nonatomic) CGPoint iconsOffset;
/// Set the visibility of this DataSet. If not visible, the DataSet will not be drawn to the chart upon refreshing it.
@property (nonatomic) BOOL visible;
/// <code>true</code> if this DataSet is visible inside the chart, or <code>false</code> ifit is currently hidden.
@property (nonatomic, readonly) BOOL isVisible;
@end

SWIFT_PROTOCOL("_TtP8DGCharts46BarLineScatterCandleBubbleChartDataSetProtocol_")
@protocol BarLineScatterCandleBubbleChartDataSetProtocol <ChartDataSetProtocol>
@property (nonatomic, strong) UIColor * _Nonnull highlightColor;
@property (nonatomic) CGFloat highlightLineWidth;
@property (nonatomic) CGFloat highlightLineDashPhase;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable highlightLineDashLengths;
@end

SWIFT_PROTOCOL("_TtP8DGCharts23BarChartDataSetProtocol_")
@protocol BarChartDataSetProtocol <BarLineScatterCandleBubbleChartDataSetProtocol>
/// <code>true</code> if this DataSet is stacked (stacksize > 1) or not.
@property (nonatomic, readonly) BOOL isStacked;
/// The maximum number of bars that can be stacked upon another in this DataSet.
@property (nonatomic, readonly) NSInteger stackSize;
/// the color used for drawing the bar-shadows. The bar shadows is a surface behind the bar that indicates the maximum value
@property (nonatomic, strong) UIColor * _Nonnull barShadowColor;
/// the width used for drawing borders around the bars. If borderWidth == 0, no border will be drawn.
@property (nonatomic) CGFloat barBorderWidth;
/// the color drawing borders around the bars.
@property (nonatomic, strong) UIColor * _Nonnull barBorderColor;
/// the alpha value (transparency) that is used for drawing the highlight indicator bar. min = 0.0 (fully transparent), max = 1.0 (fully opaque)
@property (nonatomic) CGFloat highlightAlpha;
/// array of labels used to describe the different values of the stacked bars
@property (nonatomic, copy) NSArray<NSString *> * _Nonnull stackLabels;
@end

SWIFT_CLASS("_TtC8DGCharts16ChartBaseDataSet")
@interface ChartBaseDataSet : NSObject <ChartDataSetProtocol, NSCopying>
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithLabel:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
/// Use this method to tell the data set that the underlying data has changed
- (void)notifyDataSetChanged;
- (void)calcMinMax;
- (void)calcMinMaxYFromX:(double)fromX toX:(double)toX;
@property (nonatomic, readonly) double yMin;
@property (nonatomic, readonly) double yMax;
@property (nonatomic, readonly) double xMin;
@property (nonatomic, readonly) double xMax;
@property (nonatomic, readonly) NSInteger entryCount;
- (ChartDataEntry * _Nullable)entryForIndex:(NSInteger)i SWIFT_WARN_UNUSED_RESULT;
- (ChartDataEntry * _Nullable)entryForXValue:(double)x closestToY:(double)y rounding:(enum ChartDataSetRounding)rounding SWIFT_WARN_UNUSED_RESULT;
- (ChartDataEntry * _Nullable)entryForXValue:(double)x closestToY:(double)y SWIFT_WARN_UNUSED_RESULT;
- (NSArray<ChartDataEntry *> * _Nonnull)entriesForXValue:(double)x SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)entryIndexWithX:(double)xValue closestToY:(double)y rounding:(enum ChartDataSetRounding)rounding SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)entryIndexWithEntry:(ChartDataEntry * _Nonnull)e SWIFT_WARN_UNUSED_RESULT;
- (BOOL)addEntry:(ChartDataEntry * _Nonnull)e;
- (BOOL)addEntryOrdered:(ChartDataEntry * _Nonnull)e;
- (BOOL)removeEntry:(ChartDataEntry * _Nonnull)entry;
- (BOOL)removeEntryWithIndex:(NSInteger)index;
- (BOOL)removeEntryWithX:(double)x;
- (BOOL)removeFirst;
- (BOOL)removeLast;
- (BOOL)contains:(ChartDataEntry * _Nonnull)e SWIFT_WARN_UNUSED_RESULT;
- (void)clear;
/// All the colors that are used for this DataSet.
/// Colors are reused as soon as the number of Entries the DataSet represents is higher than the size of the colors array.
@property (nonatomic, copy) NSArray<UIColor *> * _Nonnull colors;
/// List representing all colors that are used for drawing the actual values for this DataSet
@property (nonatomic, copy) NSArray<UIColor *> * _Nonnull valueColors;
/// The label string that describes the DataSet.
@property (nonatomic, copy) NSString * _Nullable label;
/// The axis this DataSet should be plotted against.
@property (nonatomic) enum AxisDependency axisDependency;
///
/// returns:
/// The color at the given index of the DataSet’s color array.
/// This prevents out-of-bounds by performing a modulus on the color index, so colours will repeat themselves.
- (UIColor * _Nonnull)colorAtIndex:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
/// Resets all colors of this DataSet and recreates the colors array.
- (void)resetColors;
/// Adds a new color to the colors array of the DataSet.
/// \param color the color to add
///
- (void)addColor:(UIColor * _Nonnull)color;
/// Sets the one and <em>only</em> color that should be used for this DataSet.
/// Internally, this recreates the colors array and adds the specified color.
/// \param color the color to set
///
- (void)setColor:(UIColor * _Nonnull)color;
/// Sets colors to a single color a specific alpha value.
/// \param color the color to set
///
/// \param alpha alpha to apply to the set <code>color</code>
///
- (void)setColor:(UIColor * _Nonnull)color alpha:(CGFloat)alpha;
/// Sets colors with a specific alpha value.
/// \param colors the colors to set
///
/// \param alpha alpha to apply to the set <code>colors</code>
///
- (void)setColors:(NSArray<UIColor *> * _Nonnull)colors alpha:(CGFloat)alpha;
/// if true, value highlighting is enabled
@property (nonatomic) BOOL highlightEnabled;
/// <code>true</code> if value highlighting is enabled for this dataset
@property (nonatomic, readonly) BOOL isHighlightEnabled;
/// Custom formatter that is used instead of the auto-formatter if set
@property (nonatomic, strong) id <ChartValueFormatter> _Nonnull valueFormatter;
/// Sets/get a single color for value text.
/// Setting the color clears the colors array and adds a single color.
/// Getting will return the first color in the array.
@property (nonatomic, strong) UIColor * _Nonnull valueTextColor;
///
/// returns:
/// The color at the specified index that is used for drawing the values inside the chart. Uses modulus internally.
- (UIColor * _Nonnull)valueTextColorAt:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
/// the font for the value-text labels
@property (nonatomic, strong) UIFont * _Nonnull valueFont;
/// The rotation angle (in degrees) for value-text labels
@property (nonatomic) CGFloat valueLabelAngle;
/// The form to draw for this dataset in the legend.
@property (nonatomic) enum ChartLegendForm form;
/// The form size to draw for this dataset in the legend.
/// Return <code>NaN</code> to use the default legend form size.
@property (nonatomic) CGFloat formSize;
/// The line width for drawing the form of this dataset in the legend
/// Return <code>NaN</code> to use the default legend form line width.
@property (nonatomic) CGFloat formLineWidth;
/// Line dash configuration for legend shapes that consist of lines.
/// This is how much (in pixels) into the dash pattern are we starting from.
@property (nonatomic) CGFloat formLineDashPhase;
/// Line dash configuration for legend shapes that consist of lines.
/// This is the actual dash pattern.
/// I.e. [2, 3] will paint [–   –   ]
/// [1, 3, 4, 2] will paint [-   ––  -   ––  ]
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable formLineDashLengths;
/// Set this to true to draw y-values on the chart.
/// note:
/// For bar and line charts: if <code>maxVisibleCount</code> is reached, no values will be drawn even if this is enabled.
@property (nonatomic) BOOL drawValuesEnabled;
/// <code>true</code> if y-value drawing is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawValuesEnabled;
/// Set this to true to draw y-icons on the chart.
/// note:
/// For bar and line charts: if <code>maxVisibleCount</code> is reached, no icons will be drawn even if this is enabled.
@property (nonatomic) BOOL drawIconsEnabled;
/// Returns true if y-icon drawing is enabled, false if not
@property (nonatomic, readonly) BOOL isDrawIconsEnabled;
/// Offset of icons drawn on the chart.
/// For all charts except Pie and Radar it will be ordinary (x offset, y offset).
/// For Pie and Radar chart it will be (y offset, distance from center offset); so if you want icon to be rendered under value, you should increase X component of CGPoint, and if you want icon to be rendered closet to center, you should decrease height component of CGPoint.
@property (nonatomic) CGPoint iconsOffset;
/// Set the visibility of this DataSet. If not visible, the DataSet will not be drawn to the chart upon refreshing it.
@property (nonatomic) BOOL visible;
/// <code>true</code> if this DataSet is visible inside the chart, or <code>false</code> ifit is currently hidden.
@property (nonatomic, readonly) BOOL isVisible;
@property (nonatomic, readonly, copy) NSString * _Nonnull description;
@property (nonatomic, readonly, copy) NSString * _Nonnull debugDescription;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
@end

/// The DataSet class represents one group or type of entries (Entry) in the Chart that belong together.
/// It is designed to logically separate different groups of values inside the Chart (e.g. the values for a specific line in the LineChart, or the values of a specific group of bars in the BarChart).
SWIFT_CLASS("_TtC8DGCharts12ChartDataSet")
@interface ChartDataSet : ChartBaseDataSet
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithLabel:(NSString * _Nonnull)label;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries;
/// note:
/// Calls <code>notifyDataSetChanged()</code> after setting a new value.
///
/// returns:
/// The array of y-values that this DataSet represents.
/// the entries that this dataset represents / holds together
@property (nonatomic, readonly, copy) NSArray<ChartDataEntry *> * _Nonnull entries;
/// Used to replace all entries of a data set while retaining styling properties.
/// This is a separate method from a setter on <code>entries</code> to encourage usage
/// of <code>Collection</code> conformances.
/// \param entries new entries to replace existing entries in the dataset
///
- (void)replaceEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries;
- (void)calcMinMax;
- (void)calcMinMaxYFromX:(double)fromX toX:(double)toX;
- (void)calcMinMaxXWithEntry:(ChartDataEntry * _Nonnull)e;
- (void)calcMinMaxYWithEntry:(ChartDataEntry * _Nonnull)e;
/// The minimum y-value this DataSet holds
@property (nonatomic, readonly) double yMin;
/// The maximum y-value this DataSet holds
@property (nonatomic, readonly) double yMax;
/// The minimum x-value this DataSet holds
@property (nonatomic, readonly) double xMin;
/// The maximum x-value this DataSet holds
@property (nonatomic, readonly) double xMax;
/// The number of y-values this DataSet represents
@property (nonatomic, readonly) NSInteger entryCount SWIFT_DEPRECATED_MSG("Use `count` instead");
///
/// throws:
/// out of bounds
/// if <code>i</code> is out of bounds, it may throw an out-of-bounds exception
///
/// returns:
/// The entry object found at the given index (not x-value!)
- (ChartDataEntry * _Nullable)entryForIndex:(NSInteger)i SWIFT_WARN_UNUSED_RESULT SWIFT_DEPRECATED_MSG("Use `subscript(index:)` instead.");
/// \param xValue the x-value
///
/// \param closestToY If there are multiple y-values for the specified x-value,
///
/// \param rounding determine whether to round up/down/closest if there is no Entry matching the provided x-value
///
///
/// returns:
/// The first Entry object found at the given x-value with binary search.
/// If the no Entry at the specified x-value is found, this method returns the Entry at the closest x-value according to the rounding.
/// nil if no Entry object at that x-value.
- (ChartDataEntry * _Nullable)entryForXValue:(double)xValue closestToY:(double)yValue rounding:(enum ChartDataSetRounding)rounding SWIFT_WARN_UNUSED_RESULT;
/// \param xValue the x-value
///
/// \param closestToY If there are multiple y-values for the specified x-value,
///
///
/// returns:
/// The first Entry object found at the given x-value with binary search.
/// If the no Entry at the specified x-value is found, this method returns the Entry at the closest x-value.
/// nil if no Entry object at that x-value.
- (ChartDataEntry * _Nullable)entryForXValue:(double)xValue closestToY:(double)yValue SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// All Entry objects found at the given xIndex with binary search.
/// An empty array if no Entry object at that index.
- (NSArray<ChartDataEntry *> * _Nonnull)entriesForXValue:(double)xValue SWIFT_WARN_UNUSED_RESULT;
/// \param xValue x-value of the entry to search for
///
/// \param closestToY If there are multiple y-values for the specified x-value,
///
/// \param rounding Rounding method if exact value was not found
///
///
/// returns:
/// The array-index of the specified entry.
/// If the no Entry at the specified x-value is found, this method returns the index of the Entry at the closest x-value according to the rounding.
- (NSInteger)entryIndexWithX:(double)xValue closestToY:(double)yValue rounding:(enum ChartDataSetRounding)rounding SWIFT_WARN_UNUSED_RESULT;
/// \param e the entry to search for
///
///
/// returns:
/// The array-index of the specified entry
- (NSInteger)entryIndexWithEntry:(ChartDataEntry * _Nonnull)e SWIFT_WARN_UNUSED_RESULT SWIFT_DEPRECATED_MSG("Use `firstIndex(of:)` or `lastIndex(of:)`");
/// Adds an Entry to the DataSet dynamically.
/// Entries are added to the end of the list.
/// This will also recalculate the current minimum and maximum values of the DataSet and the value-sum.
/// \param e the entry to add
///
///
/// returns:
/// True
- (BOOL)addEntry:(ChartDataEntry * _Nonnull)e SWIFT_DEPRECATED_MSG("Use `append(_:)` instead", "append(_:)");
/// Adds an Entry to the DataSet dynamically.
/// Entries are added to their appropriate index respective to it’s x-index.
/// This will also recalculate the current minimum and maximum values of the DataSet and the value-sum.
/// \param e the entry to add
///
///
/// returns:
/// True
- (BOOL)addEntryOrdered:(ChartDataEntry * _Nonnull)e;
- (BOOL)removeEntry:(ChartDataEntry * _Nonnull)entry SWIFT_WARN_UNUSED_RESULT;
/// Removes the first Entry (at index 0) of this DataSet from the entries array.
///
/// returns:
/// <code>true</code> if successful, <code>false</code> if not.
- (BOOL)removeFirst SWIFT_WARN_UNUSED_RESULT SWIFT_DEPRECATED_MSG("Use `func removeFirst() -> ChartDataEntry` instead.");
/// Removes the last Entry (at index size-1) of this DataSet from the entries array.
///
/// returns:
/// <code>true</code> if successful, <code>false</code> if not.
- (BOOL)removeLast SWIFT_WARN_UNUSED_RESULT SWIFT_DEPRECATED_MSG("Use `func removeLast() -> ChartDataEntry` instead.");
/// Removes all values from this DataSet and recalculates min and max value.
- (void)clear SWIFT_DEPRECATED_MSG("Use `removeAll(keepingCapacity:)` instead.");
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_CLASS("_TtC8DGCharts38BarLineScatterCandleBubbleChartDataSet")
@interface BarLineScatterCandleBubbleChartDataSet : ChartDataSet <BarLineScatterCandleBubbleChartDataSetProtocol>
@property (nonatomic, strong) UIColor * _Nonnull highlightColor;
@property (nonatomic) CGFloat highlightLineWidth;
@property (nonatomic) CGFloat highlightLineDashPhase;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable highlightLineDashLengths;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts15BarChartDataSet")
@interface BarChartDataSet : BarLineScatterCandleBubbleChartDataSet <BarChartDataSetProtocol>
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
/// The maximum number of bars that can be stacked upon another in this DataSet.
@property (nonatomic, readonly) NSInteger stackSize;
/// <code>true</code> if this DataSet is stacked (stacksize > 1) or not.
@property (nonatomic, readonly) BOOL isStacked;
/// The overall entry count, including counting each stack-value individually
@property (nonatomic, readonly) NSInteger entryCountStacks;
/// array of labels used to describe the different values of the stacked bars
@property (nonatomic, copy) NSArray<NSString *> * _Nonnull stackLabels;
/// the color used for drawing the bar-shadows. The bar shadows is a surface behind the bar that indicates the maximum value
@property (nonatomic, strong) UIColor * _Nonnull barShadowColor;
/// the width used for drawing borders around the bars. If borderWidth == 0, no border will be drawn.
@property (nonatomic) CGFloat barBorderWidth;
/// the color drawing borders around the bars.
@property (nonatomic, strong) UIColor * _Nonnull barBorderColor;
/// the alpha value (transparency) that is used for drawing the highlight indicator bar. min = 0.0 (fully transparent), max = 1.0 (fully opaque)
@property (nonatomic) CGFloat highlightAlpha;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_PROTOCOL_NAMED("Renderer")
@protocol ChartRenderer
/// the component that handles the drawing area of the chart and it’s offsets
@property (nonatomic, readonly, strong) ChartViewPortHandler * _Nonnull viewPortHandler;
@end

@class NSUIAccessibilityElement;
SWIFT_PROTOCOL_NAMED("DataRenderer")
@protocol ChartDataRenderer <ChartRenderer>
/// An array of accessibility elements that are presented to the ChartViewBase accessibility methods.
/// Note that the order of elements in this array determines the order in which they are presented and navigated by
/// Accessibility clients such as VoiceOver.
/// Renderers should ensure that the order of elements makes sense to a client presenting an audio-only interface to a user.
/// Subclasses should populate this array in drawData() or drawDataSet() to make the chart accessible.
@property (nonatomic, readonly, copy) NSArray<NSUIAccessibilityElement *> * _Nonnull accessibleChartElements;
@property (nonatomic, readonly, strong) ChartAnimator * _Nonnull animator;
- (void)drawDataWithContext:(CGContextRef _Nonnull)context;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
- (void)drawExtrasWithContext:(CGContextRef _Nonnull)context;
/// Draws all highlight indicators for the values that are currently highlighted.
/// \param indices the highlighted values
///
- (void)drawHighlightedWithContext:(CGContextRef _Nonnull)context indices:(NSArray<ChartHighlight *> * _Nonnull)indices;
/// An opportunity for initializing internal buffers used for rendering with a new size.
/// Since this might do memory allocations, it should only be called if necessary.
- (void)initBuffers SWIFT_METHOD_FAMILY(none);
- (BOOL)isDrawingValuesAllowedWithDataProvider:(id <ChartDataProvider> _Nullable)dataProvider SWIFT_WARN_UNUSED_RESULT;
/// Creates an <code>NSUIAccessibilityElement</code> that acts as the first and primary header describing a chart view.
/// \param chart The chartView object being described
///
/// \param data A non optional data source about the chart
///
/// \param defaultDescription A simple string describing the type/design of Chart.
///
///
/// returns:
/// A header <code>NSUIAccessibilityElement</code> that can be added to accessibleChartElements.
- (NSUIAccessibilityElement * _Nonnull)createAccessibleHeaderUsingChart:(ChartViewBase * _Nonnull)chart andData:(ChartData * _Nonnull)data withDefaultDescription:(NSString * _Nonnull)defaultDescription SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_CLASS_NAMED("BarLineScatterCandleBubbleRenderer")
@interface BarLineScatterCandleBubbleChartRenderer : NSObject <ChartDataRenderer>
@property (nonatomic, readonly, strong) ChartViewPortHandler * _Nonnull viewPortHandler;
@property (nonatomic, copy) NSArray<NSUIAccessibilityElement *> * _Nonnull accessibleChartElements;
@property (nonatomic, readonly, strong) ChartAnimator * _Nonnull animator;
- (void)drawDataWithContext:(CGContextRef _Nonnull)context;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
- (void)drawExtrasWithContext:(CGContextRef _Nonnull)context;
- (void)drawHighlightedWithContext:(CGContextRef _Nonnull)context indices:(NSArray<ChartHighlight *> * _Nonnull)indices;
- (void)initBuffers SWIFT_METHOD_FAMILY(none);
- (BOOL)isDrawingValuesAllowedWithDataProvider:(id <ChartDataProvider> _Nullable)dataProvider SWIFT_WARN_UNUSED_RESULT;
- (NSUIAccessibilityElement * _Nonnull)createAccessibleHeaderUsingChart:(ChartViewBase * _Nonnull)chart andData:(ChartData * _Nonnull)data withDefaultDescription:(NSString * _Nonnull)defaultDescription SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS("_TtC8DGCharts16BarChartRenderer")
@interface BarChartRenderer : BarLineScatterCandleBubbleChartRenderer
@property (nonatomic, weak) id <BarChartDataProvider> _Nullable dataProvider;
- (nonnull instancetype)initWithDataProvider:(id <BarChartDataProvider> _Nonnull)dataProvider animator:(ChartAnimator * _Nonnull)animator viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
- (void)initBuffers SWIFT_METHOD_FAMILY(none);
- (void)drawDataWithContext:(CGContextRef _Nonnull)context;
- (void)drawDataSetWithContext:(CGContextRef _Nonnull)context dataSet:(id <BarChartDataSetProtocol> _Nonnull)dataSet index:(NSInteger)index;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
/// Draws a value at the specified x and y position.
- (void)drawValueWithContext:(CGContextRef _Nonnull)context value:(NSString * _Nonnull)value xPos:(CGFloat)xPos yPos:(CGFloat)yPos font:(UIFont * _Nonnull)font align:(NSTextAlignment)align color:(UIColor * _Nonnull)color anchor:(CGPoint)anchor angleRadians:(CGFloat)angleRadians;
- (void)drawExtrasWithContext:(CGContextRef _Nonnull)context;
- (void)drawHighlightedWithContext:(CGContextRef _Nonnull)context indices:(NSArray<ChartHighlight *> * _Nonnull)indices;
@end

@class NSCoder;
SWIFT_CLASS("_TtC8DGCharts8NSUIView")
@interface NSUIView : UIView
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end

@class ChartXAxis;
@class ChartDescription;
@class ChartLegend;
@protocol ChartViewDelegate;
@class ChartLegendRenderer;
@protocol ChartHighlighter;
@protocol ChartMarker;
@class UITouch;
@class UIEvent;
SWIFT_CLASS("_TtC8DGCharts13ChartViewBase")
@interface ChartViewBase : NSUIView <ChartAnimatorDelegate, ChartDataProvider>
/// object that holds all data that was originally set for the chart, before it was modified or any filtering algorithms had been applied
@property (nonatomic, strong) ChartData * _Nullable data;
/// If set to true, chart continues to scroll after touch up
@property (nonatomic) BOOL dragDecelerationEnabled;
/// The object representing the labels on the x-axis
@property (nonatomic, readonly, strong) ChartXAxis * _Nonnull xAxis;
/// The <code>Description</code> object of the chart.
@property (nonatomic, strong) ChartDescription * _Nonnull chartDescription;
/// The legend object containing all data associated with the legend
@property (nonatomic, readonly, strong) ChartLegend * _Nonnull legend;
/// delegate to receive chart events
@property (nonatomic, weak) id <ChartViewDelegate> _Nullable delegate;
/// text that is displayed when the chart is empty
@property (nonatomic, copy) NSString * _Nonnull noDataText;
/// Font to be used for the no data text.
@property (nonatomic, strong) UIFont * _Nonnull noDataFont;
/// color of the no data text
@property (nonatomic, strong) UIColor * _Nonnull noDataTextColor;
/// alignment of the no data text
@property (nonatomic) NSTextAlignment noDataTextAlignment;
/// The renderer object responsible for rendering / drawing the Legend.
@property (nonatomic, strong) ChartLegendRenderer * _Nonnull legendRenderer;
/// object responsible for rendering the data
@property (nonatomic, strong) id <ChartDataRenderer> _Nullable renderer;
@property (nonatomic, strong) id <ChartHighlighter> _Nullable highlighter;
/// The ViewPortHandler of the chart that is responsible for the
/// content area of the chart and its offsets and dimensions.
@property (nonatomic, readonly, strong) ChartViewPortHandler * _Nonnull viewPortHandler;
/// The animator responsible for animating chart values.
@property (nonatomic, readonly, strong) ChartAnimator * _Nonnull chartAnimator;
/// The array of currently highlighted values. This might an empty if nothing is highlighted.
@property (nonatomic, readonly, copy) NSArray<ChartHighlight *> * _Nonnull highlighted;
/// <code>true</code> if drawing the marker is enabled when tapping on values
/// (use the <code>marker</code> property to specify a marker)
@property (nonatomic) BOOL drawMarkers;
///
/// returns:
/// <code>true</code> if drawing the marker is enabled when tapping on values
/// (use the <code>marker</code> property to specify a marker)
@property (nonatomic, readonly) BOOL isDrawMarkersEnabled;
/// The marker that is displayed when a value is clicked on the chart
@property (nonatomic, strong) id <ChartMarker> _Nullable marker;
/// An extra offset to be appended to the viewport’s top
@property (nonatomic) CGFloat extraTopOffset;
/// An extra offset to be appended to the viewport’s right
@property (nonatomic) CGFloat extraRightOffset;
/// An extra offset to be appended to the viewport’s bottom
@property (nonatomic) CGFloat extraBottomOffset;
/// An extra offset to be appended to the viewport’s left
@property (nonatomic) CGFloat extraLeftOffset;
- (void)setExtraOffsetsWithLeft:(CGFloat)left top:(CGFloat)top right:(CGFloat)right bottom:(CGFloat)bottom;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
/// Clears the chart from all data (sets it to null) and refreshes it (by calling setNeedsDisplay()).
- (void)clear;
/// Removes all DataSets (and thereby Entries) from the chart. Does not set the data object to nil. Also refreshes the chart by calling setNeedsDisplay().
- (void)clearValues;
///
/// returns:
/// <code>true</code> if the chart is empty (meaning it’s data object is either null or contains no entries).
- (BOOL)isEmpty SWIFT_WARN_UNUSED_RESULT;
/// Lets the chart know its underlying data has changed and should perform all necessary recalculations.
/// It is crucial that this method is called everytime data is changed dynamically. Not calling this method can lead to crashes or unexpected behaviour.
- (void)notifyDataSetChanged;
- (void)drawRect:(CGRect)rect;
- (NSArray * _Nullable)accessibilityChildren SWIFT_WARN_UNUSED_RESULT;
/// Set this to false to prevent values from being highlighted by tap gesture.
/// Values can still be highlighted via drag or programmatically.
/// <em>default</em>: true
@property (nonatomic) BOOL highlightPerTapEnabled;
/// <code>true</code> if values can be highlighted via tap gesture, <code>false</code> ifnot.
@property (nonatomic, readonly) BOOL isHighLightPerTapEnabled;
/// Checks if the highlight array is null, has a length of zero or if the first object is null.
///
/// returns:
/// <code>true</code> if there are values to highlight, <code>false</code> ifthere are no values to highlight.
- (BOOL)valuesToHighlight SWIFT_WARN_UNUSED_RESULT;
/// Highlights the values at the given indices in the given DataSets. Provide
/// null or an empty array to undo all highlighting.
/// This should be used to programmatically highlight values.
/// This method <em>will not</em> call the delegate.
- (void)highlightValues:(NSArray<ChartHighlight *> * _Nullable)highs;
/// Highlights any y-value at the given x-value in the given DataSet.
/// Provide -1 as the dataSetIndex to undo all highlighting.
/// This method will call the delegate.
/// \param x The x-value to highlight
///
/// \param dataSetIndex The dataset index to search in
///
/// \param dataIndex The data index to search in (only used in CombinedChartView currently)
///
- (void)highlightValueWithX:(double)x dataSetIndex:(NSInteger)dataSetIndex dataIndex:(NSInteger)dataIndex;
/// Highlights the value at the given x-value and y-value in the given DataSet.
/// Provide -1 as the dataSetIndex to undo all highlighting.
/// This method will call the delegate.
/// \param x The x-value to highlight
///
/// \param y The y-value to highlight. Supply <code>NaN</code> for “any”
///
/// \param dataSetIndex The dataset index to search in
///
/// \param dataIndex The data index to search in (only used in CombinedChartView currently)
///
- (void)highlightValueWithX:(double)x y:(double)y dataSetIndex:(NSInteger)dataSetIndex dataIndex:(NSInteger)dataIndex;
/// Highlights any y-value at the given x-value in the given DataSet.
/// Provide -1 as the dataSetIndex to undo all highlighting.
/// \param x The x-value to highlight
///
/// \param dataSetIndex The dataset index to search in
///
/// \param dataIndex The data index to search in (only used in CombinedChartView currently)
///
/// \param callDelegate Should the delegate be called for this change
///
- (void)highlightValueWithX:(double)x dataSetIndex:(NSInteger)dataSetIndex dataIndex:(NSInteger)dataIndex callDelegate:(BOOL)callDelegate;
/// Highlights the value at the given x-value and y-value in the given DataSet.
/// Provide -1 as the dataSetIndex to undo all highlighting.
/// \param x The x-value to highlight
///
/// \param y The y-value to highlight. Supply <code>NaN</code> for “any”
///
/// \param dataSetIndex The dataset index to search in
///
/// \param dataIndex The data index to search in (only used in CombinedChartView currently)
///
/// \param callDelegate Should the delegate be called for this change
///
- (void)highlightValueWithX:(double)x y:(double)y dataSetIndex:(NSInteger)dataSetIndex dataIndex:(NSInteger)dataIndex callDelegate:(BOOL)callDelegate;
/// Highlights the values represented by the provided Highlight object
/// This method <em>will not</em> call the delegate.
/// \param highlight contains information about which entry should be highlighted
///
- (void)highlightValue:(ChartHighlight * _Nullable)highlight;
/// Highlights the value selected by touch gesture.
- (void)highlightValue:(ChartHighlight * _Nullable)highlight callDelegate:(BOOL)callDelegate;
///
/// returns:
/// The Highlight object (contains x-index and DataSet index) of the
/// selected value at the given touch point inside the Line-, Scatter-, or
/// CandleStick-Chart.
- (ChartHighlight * _Nullable)getHighlightByTouchPoint:(CGPoint)pt SWIFT_WARN_UNUSED_RESULT;
/// The last value that was highlighted via touch.
@property (nonatomic, strong) ChartHighlight * _Nullable lastHighlighted;
///
/// returns:
/// The actual position in pixels of the MarkerView for the given Entry in the given DataSet.
- (CGPoint)getMarkerPositionWithHighlight:(ChartHighlight * _Nonnull)highlight SWIFT_WARN_UNUSED_RESULT;
/// Animates the drawing / rendering of the chart on both x- and y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param yAxisDuration duration for animating the y axis
///
/// \param easingX an easing function for the animation on the x axis
///
/// \param easingY an easing function for the animation on the y axis
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration yAxisDuration:(NSTimeInterval)yAxisDuration easingX:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easingX easingY:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easingY;
/// Animates the drawing / rendering of the chart on both x- and y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param yAxisDuration duration for animating the y axis
///
/// \param easingOptionX the easing function for the animation on the x axis
///
/// \param easingOptionY the easing function for the animation on the y axis
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration yAxisDuration:(NSTimeInterval)yAxisDuration easingOptionX:(enum ChartEasingOption)easingOptionX easingOptionY:(enum ChartEasingOption)easingOptionY;
/// Animates the drawing / rendering of the chart on both x- and y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param yAxisDuration duration for animating the y axis
///
/// \param easing an easing function for the animation
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration yAxisDuration:(NSTimeInterval)yAxisDuration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing;
/// Animates the drawing / rendering of the chart on both x- and y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param yAxisDuration duration for animating the y axis
///
/// \param easingOption the easing function for the animation
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration yAxisDuration:(NSTimeInterval)yAxisDuration easingOption:(enum ChartEasingOption)easingOption;
/// Animates the drawing / rendering of the chart on both x- and y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param yAxisDuration duration for animating the y axis
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration yAxisDuration:(NSTimeInterval)yAxisDuration;
/// Animates the drawing / rendering of the chart the x-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param easing an easing function for the animation
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing;
/// Animates the drawing / rendering of the chart the x-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
/// \param easingOption the easing function for the animation
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration easingOption:(enum ChartEasingOption)easingOption;
/// Animates the drawing / rendering of the chart the x-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param xAxisDuration duration for animating the x axis
///
- (void)animateWithXAxisDuration:(NSTimeInterval)xAxisDuration;
/// Animates the drawing / rendering of the chart the y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param yAxisDuration duration for animating the y axis
///
/// \param easing an easing function for the animation
///
- (void)animateWithYAxisDuration:(NSTimeInterval)yAxisDuration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing;
/// Animates the drawing / rendering of the chart the y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param yAxisDuration duration for animating the y axis
///
/// \param easingOption the easing function for the animation
///
- (void)animateWithYAxisDuration:(NSTimeInterval)yAxisDuration easingOption:(enum ChartEasingOption)easingOption;
/// Animates the drawing / rendering of the chart the y-axis with the specified animation time.
/// If <code>animate(...)</code> is called, no further calling of <code>invalidate()</code> is necessary to refresh the chart.
/// \param yAxisDuration duration for animating the y axis
///
- (void)animateWithYAxisDuration:(NSTimeInterval)yAxisDuration;
/// The current y-max value across all DataSets
@property (nonatomic, readonly) double chartYMax;
/// The current y-min value across all DataSets
@property (nonatomic, readonly) double chartYMin;
@property (nonatomic, readonly) double chartXMax;
@property (nonatomic, readonly) double chartXMin;
@property (nonatomic, readonly) double xRange;
/// note:
/// (Equivalent of getCenter() in MPAndroidChart, as center is already a standard in iOS that returns the center point relative to superview, and MPAndroidChart returns relative to self)*
/// The center point of the chart (the whole View) in pixels.
@property (nonatomic, readonly) CGPoint midPoint;
/// The center of the chart taking offsets under consideration. (returns the center of the content rectangle)
@property (nonatomic, readonly) CGPoint centerOffsets;
/// The rectangle that defines the borders of the chart-value surface (into which the actual values are drawn).
@property (nonatomic, readonly) CGRect contentRect;
///
/// returns:
/// The bitmap that represents the chart.
- (UIImage * _Nullable)getChartImageWithTransparent:(BOOL)transparent SWIFT_WARN_UNUSED_RESULT;
- (void)observeValueForKeyPath:(NSString * _Nullable)keyPath ofObject:(id _Nullable)object change:(NSDictionary<NSKeyValueChangeKey, id> * _Nullable)change context:(void * _Nullable)context;
- (void)removeViewportJob:(ChartViewPortJob * _Nonnull)job;
- (void)clearAllViewportJobs;
- (void)addViewportJob:(ChartViewPortJob * _Nonnull)job;
/// <em>default</em>: true
/// <code>true</code> if chart continues to scroll after touch up, <code>false</code> ifnot.
@property (nonatomic, readonly) BOOL isDragDecelerationEnabled;
/// Deceleration friction coefficient in [0 ; 1] interval, higher values indicate that speed will decrease slowly, for example if it set to 0, it will stop immediately.
/// 1 is an invalid value, and will be converted to 0.999 automatically.
@property (nonatomic) CGFloat dragDecelerationFrictionCoef;
/// The maximum distance in screen pixels away from an entry causing it to highlight.
/// <em>default</em>: 500.0
@property (nonatomic) CGFloat maxHighlightDistance;
/// the number of maximum visible drawn values on the chart only active when <code>drawValuesEnabled</code> is enabled
@property (nonatomic, readonly) NSInteger maxVisibleCount;
- (void)animatorUpdated:(ChartAnimator * _Nonnull)chartAnimator;
- (void)animatorStopped:(ChartAnimator * _Nonnull)chartAnimator;
- (void)nsuiTouchesBegan:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)nsuiTouchesMoved:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)nsuiTouchesEnded:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)nsuiTouchesCancelled:(NSSet<UITouch *> * _Nullable)touches withEvent:(UIEvent * _Nullable)event;
@end

@class ChartYAxisRenderer;
@class ChartXAxisRenderer;
@class UIGestureRecognizer;
/// Base-class of LineChart, BarChart, ScatterChart and CandleStickChart.
SWIFT_CLASS("_TtC8DGCharts20BarLineChartViewBase")
@interface BarLineChartViewBase : ChartViewBase <BarLineScatterCandleBubbleChartDataProvider, UIGestureRecognizerDelegate>
/// the color for the background of the chart-drawing area (everything behind the grid lines).
@property (nonatomic, strong) UIColor * _Nonnull gridBackgroundColor;
@property (nonatomic, strong) UIColor * _Nonnull borderColor;
@property (nonatomic) CGFloat borderLineWidth;
/// flag indicating if the grid background should be drawn or not
@property (nonatomic) BOOL drawGridBackgroundEnabled;
/// When enabled, the borders rectangle will be rendered.
/// If this is enabled, there is no point drawing the axis-lines of x- and y-axis.
@property (nonatomic) BOOL drawBordersEnabled;
/// When enabled, the values will be clipped to contentRect, otherwise they can bleed outside the content rect.
@property (nonatomic) BOOL clipValuesToContentEnabled;
/// When disabled, the data and/or highlights will not be clipped to contentRect. Disabling this option can
/// be useful, when the data lies fully within the content rect, but is drawn in such a way (such as thick lines)
/// that there is unwanted clipping.
@property (nonatomic) BOOL clipDataToContentEnabled;
/// Sets the minimum offset (padding) around the chart, defaults to 10
@property (nonatomic) CGFloat minOffset;
/// Sets whether the chart should keep its position (zoom / scroll) after a rotation (orientation change)
/// <em>default</em>: false
@property (nonatomic) BOOL keepPositionOnRotation;
/// The left y-axis object. In the horizontal bar-chart, this is the
/// top axis.
@property (nonatomic, readonly, strong) ChartYAxis * _Nonnull leftAxis;
/// The right y-axis object. In the horizontal bar-chart, this is the
/// bottom axis.
@property (nonatomic, readonly, strong) ChartYAxis * _Nonnull rightAxis;
/// The left Y axis renderer. This is a read-write property so you can set your own custom renderer here.
/// <em>default</em>: An instance of YAxisRenderer
@property (nonatomic, strong) ChartYAxisRenderer * _Nonnull leftYAxisRenderer;
/// The right Y axis renderer. This is a read-write property so you can set your own custom renderer here.
/// <em>default</em>: An instance of YAxisRenderer
@property (nonatomic, strong) ChartYAxisRenderer * _Nonnull rightYAxisRenderer;
/// The X axis renderer. This is a read-write property so you can set your own custom renderer here.
/// <em>default</em>: An instance of XAxisRenderer
@property (nonatomic, strong) ChartXAxisRenderer * _Nonnull xAxisRenderer;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
- (void)observeValueForKeyPath:(NSString * _Nullable)keyPath ofObject:(id _Nullable)object change:(NSDictionary<NSKeyValueChangeKey, id> * _Nullable)change context:(void * _Nullable)context;
- (void)drawRect:(CGRect)rect;
- (void)notifyDataSetChanged;
- (void)stopDeceleration;
- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer * _Nonnull)gestureRecognizer SWIFT_WARN_UNUSED_RESULT;
- (BOOL)gestureRecognizer:(UIGestureRecognizer * _Nonnull)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer * _Nonnull)otherGestureRecognizer SWIFT_WARN_UNUSED_RESULT;
/// Zooms in by 1.4, into the charts center.
- (void)zoomIn;
/// Zooms out by 0.7, from the charts center.
- (void)zoomOut;
/// Zooms out to original size.
- (void)resetZoom;
/// Zooms in or out by the given scale factor. x and y are the coordinates
/// (in pixels) of the zoom center.
/// \param scaleX if < 1 –> zoom out, if > 1 –> zoom in
///
/// \param scaleY if < 1 –> zoom out, if > 1 –> zoom in
///
/// \param x 
///
/// \param y 
///
- (void)zoomWithScaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY x:(CGFloat)x y:(CGFloat)y;
/// Zooms in or out by the given scale factor.
/// x and y are the values (<em>not pixels</em>) of the zoom center.
/// \param scaleX if < 1 –> zoom out, if > 1 –> zoom in
///
/// \param scaleY if < 1 –> zoom out, if > 1 –> zoom in
///
/// \param xValue 
///
/// \param yValue 
///
/// \param axis 
///
- (void)zoomWithScaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY xValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis;
/// Zooms to the center of the chart with the given scale factor.
/// \param scaleX if < 1 –> zoom out, if > 1 –> zoom in
///
/// \param scaleY if < 1 –> zoom out, if > 1 –> zoom in
///
/// \param xValue 
///
/// \param yValue 
///
/// \param axis 
///
- (void)zoomToCenterWithScaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY;
/// Zooms by the specified scale factor to the specified values on the specified axis.
/// \param scaleX 
///
/// \param scaleY 
///
/// \param xValue 
///
/// \param yValue 
///
/// \param axis which axis should be used as a reference for the y-axis
///
/// \param duration the duration of the animation in seconds
///
/// \param easing 
///
- (void)zoomAndCenterViewAnimatedWithScaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY xValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis duration:(NSTimeInterval)duration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing;
/// Zooms by the specified scale factor to the specified values on the specified axis.
/// \param scaleX 
///
/// \param scaleY 
///
/// \param xValue 
///
/// \param yValue 
///
/// \param axis which axis should be used as a reference for the y-axis
///
/// \param duration the duration of the animation in seconds
///
/// \param easing 
///
- (void)zoomAndCenterViewAnimatedWithScaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY xValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis duration:(NSTimeInterval)duration easingOption:(enum ChartEasingOption)easingOption;
/// Zooms by the specified scale factor to the specified values on the specified axis.
/// \param scaleX 
///
/// \param scaleY 
///
/// \param xValue 
///
/// \param yValue 
///
/// \param axis which axis should be used as a reference for the y-axis
///
/// \param duration the duration of the animation in seconds
///
/// \param easing 
///
- (void)zoomAndCenterViewAnimatedWithScaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY xValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis duration:(NSTimeInterval)duration;
/// Resets all zooming and dragging and makes the chart fit exactly it’s bounds.
- (void)fitScreen;
/// Sets the minimum scale value to which can be zoomed out. 1 = fitScreen
- (void)setScaleMinima:(CGFloat)scaleX scaleY:(CGFloat)scaleY;
@property (nonatomic, readonly) double visibleXRange;
/// Sets the size of the area (range on the x-axis) that should be maximum visible at once (no further zooming out allowed).
/// If this is e.g. set to 10, no more than a range of 10 values on the x-axis can be viewed at once without scrolling.
/// If you call this method, chart must have data or it has no effect.
- (void)setVisibleXRangeMaximum:(double)maxXRange;
/// Sets the size of the area (range on the x-axis) that should be minimum visible at once (no further zooming in allowed).
/// If this is e.g. set to 10, no less than a range of 10 values on the x-axis can be viewed at once without scrolling.
/// If you call this method, chart must have data or it has no effect.
- (void)setVisibleXRangeMinimum:(double)minXRange;
/// Limits the maximum and minimum value count that can be visible by pinching and zooming.
/// e.g. minRange=10, maxRange=100 no less than 10 values and no more that 100 values can be viewed
/// at once without scrolling.
/// If you call this method, chart must have data or it has no effect.
- (void)setVisibleXRangeWithMinXRange:(double)minXRange maxXRange:(double)maxXRange;
/// Sets the size of the area (range on the y-axis) that should be maximum visible at once.
/// \param yRange 
///
/// \param axis - the axis for which this limit should apply
///
- (void)setVisibleYRangeMaximum:(double)maxYRange axis:(enum AxisDependency)axis;
/// Sets the size of the area (range on the y-axis) that should be minimum visible at once, no further zooming in possible.
/// \param yRange 
///
/// \param axis - the axis for which this limit should apply
///
- (void)setVisibleYRangeMinimum:(double)minYRange axis:(enum AxisDependency)axis;
/// Limits the maximum and minimum y range that can be visible by pinching and zooming.
/// \param minYRange 
///
/// \param maxYRange 
///
/// \param axis 
///
- (void)setVisibleYRangeWithMinYRange:(double)minYRange maxYRange:(double)maxYRange axis:(enum AxisDependency)axis;
/// Moves the left side of the current viewport to the specified x-value.
/// This also refreshes the chart by calling setNeedsDisplay().
- (void)moveViewToX:(double)xValue;
/// Centers the viewport to the specified y-value on the y-axis.
/// This also refreshes the chart by calling setNeedsDisplay().
/// \param yValue 
///
/// \param axis - which axis should be used as a reference for the y-axis
///
- (void)moveViewToY:(double)yValue axis:(enum AxisDependency)axis;
/// This will move the left side of the current viewport to the specified x-value on the x-axis, and center the viewport to the specified y-value on the y-axis.
/// This also refreshes the chart by calling setNeedsDisplay().
/// \param xValue 
///
/// \param yValue 
///
/// \param axis - which axis should be used as a reference for the y-axis
///
- (void)moveViewToXValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis;
/// This will move the left side of the current viewport to the specified x-position and center the viewport to the specified y-position animated.
/// This also refreshes the chart by calling setNeedsDisplay().
/// \param xValue 
///
/// \param yValue 
///
/// \param axis which axis should be used as a reference for the y-axis
///
/// \param duration the duration of the animation in seconds
///
/// \param easing 
///
- (void)moveViewToAnimatedWithXValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis duration:(NSTimeInterval)duration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing;
/// This will move the left side of the current viewport to the specified x-position and center the viewport to the specified y-position animated.
/// This also refreshes the chart by calling setNeedsDisplay().
/// \param xValue 
///
/// \param yValue 
///
/// \param axis which axis should be used as a reference for the y-axis
///
/// \param duration the duration of the animation in seconds
///
/// \param easing 
///
- (void)moveViewToAnimatedWithXValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis duration:(NSTimeInterval)duration easingOption:(enum ChartEasingOption)easingOption;
/// This will move the left side of the current viewport to the specified x-position and center the viewport to the specified y-position animated.
/// This also refreshes the chart by calling setNeedsDisplay().
/// \param xValue 
///
/// \param yValue 
///
/// \param axis which axis should be used as a reference for the y-axis
///
/// \param duration the duration of the animation in seconds
///
/// \param easing 
///
- (void)moveViewToAnimatedWithXValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis duration:(NSTimeInterval)duration;
/// This will move the center of the current viewport to the specified x-value and y-value.
/// This also refreshes the chart by calling setNeedsDisplay().
/// \param xValue 
///
/// \param yValue 
///
/// \param axis - which axis should be used as a reference for the y-axis
///
- (void)centerViewToXValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis;
/// This will move the center of the current viewport to the specified x-value and y-value animated.
/// \param xValue 
///
/// \param yValue 
///
/// \param axis which axis should be used as a reference for the y-axis
///
/// \param duration the duration of the animation in seconds
///
/// \param easing 
///
- (void)centerViewToAnimatedWithXValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis duration:(NSTimeInterval)duration easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing;
/// This will move the center of the current viewport to the specified x-value and y-value animated.
/// \param xValue 
///
/// \param yValue 
///
/// \param axis which axis should be used as a reference for the y-axis
///
/// \param duration the duration of the animation in seconds
///
/// \param easing 
///
- (void)centerViewToAnimatedWithXValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis duration:(NSTimeInterval)duration easingOption:(enum ChartEasingOption)easingOption;
/// This will move the center of the current viewport to the specified x-value and y-value animated.
/// \param xValue 
///
/// \param yValue 
///
/// \param axis which axis should be used as a reference for the y-axis
///
/// \param duration the duration of the animation in seconds
///
/// \param easing 
///
- (void)centerViewToAnimatedWithXValue:(double)xValue yValue:(double)yValue axis:(enum AxisDependency)axis duration:(NSTimeInterval)duration;
/// Sets custom offsets for the current <code>ChartViewPort</code> (the offsets on the sides of the actual chart window). Setting this will prevent the chart from automatically calculating it’s offsets. Use <code>resetViewPortOffsets()</code> to undo this.
/// ONLY USE THIS WHEN YOU KNOW WHAT YOU ARE DOING, else use <code>setExtraOffsets(...)</code>.
- (void)setViewPortOffsetsWithLeft:(CGFloat)left top:(CGFloat)top right:(CGFloat)right bottom:(CGFloat)bottom;
/// Resets all custom offsets set via <code>setViewPortOffsets(...)</code> method. Allows the chart to again calculate all offsets automatically.
- (void)resetViewPortOffsets;
///
/// returns:
/// The range of the specified axis.
- (double)getAxisRangeWithAxis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The position (in pixels) the provided Entry has inside the chart view
- (CGPoint)getPositionWithEntry:(ChartDataEntry * _Nonnull)e axis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
/// is dragging enabled? (moving the chart with the finger) for the chart (this does not affect scaling).
@property (nonatomic) BOOL dragEnabled;
/// is dragging enabled? (moving the chart with the finger) for the chart (this does not affect scaling).
@property (nonatomic, readonly) BOOL isDragEnabled;
/// is dragging on the X axis enabled?
@property (nonatomic) BOOL dragXEnabled;
/// is dragging on the Y axis enabled?
@property (nonatomic) BOOL dragYEnabled;
/// is scaling enabled? (zooming in and out by gesture) for the chart (this does not affect dragging).
- (void)setScaleEnabled:(BOOL)enabled;
@property (nonatomic) BOOL scaleXEnabled;
@property (nonatomic) BOOL scaleYEnabled;
@property (nonatomic, readonly) BOOL isScaleXEnabled;
@property (nonatomic, readonly) BOOL isScaleYEnabled;
/// flag that indicates if double tap zoom is enabled or not
@property (nonatomic) BOOL doubleTapToZoomEnabled;
/// <em>default</em>: true
/// <code>true</code> if zooming via double-tap is enabled <code>false</code> ifnot.
@property (nonatomic, readonly) BOOL isDoubleTapToZoomEnabled;
/// flag that indicates if highlighting per dragging over a fully zoomed out chart is enabled
@property (nonatomic) BOOL highlightPerDragEnabled;
/// If set to true, highlighting per dragging over a fully zoomed out chart is enabled
/// You might want to disable this when using inside a <code>NSUIScrollView</code>
/// <em>default</em>: true
@property (nonatomic, readonly) BOOL isHighlightPerDragEnabled;
/// <em>default</em>: true
/// <code>true</code> if drawing the grid background is enabled, <code>false</code> ifnot.
@property (nonatomic, readonly) BOOL isDrawGridBackgroundEnabled;
/// <em>default</em>: false
/// <code>true</code> if drawing the borders rectangle is enabled, <code>false</code> ifnot.
@property (nonatomic, readonly) BOOL isDrawBordersEnabled;
///
/// returns:
/// The x and y values in the chart at the given touch point
/// (encapsulated in a <code>CGPoint</code>). This method transforms pixel coordinates to
/// coordinates / values in the chart. This is the opposite method to
/// <code>getPixelsForValues(...)</code>.
- (CGPoint)valueForTouchPointWithPoint:(CGPoint)pt axis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
/// Transforms the given chart values into pixels. This is the opposite
/// method to <code>valueForTouchPoint(...)</code>.
- (CGPoint)pixelForValuesWithX:(double)x y:(double)y axis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The Entry object displayed at the touched position of the chart
- (ChartDataEntry * _Null_unspecified)getEntryByTouchPointWithPoint:(CGPoint)pt SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The DataSet object displayed at the touched position of the chart
- (id <BarLineScatterCandleBubbleChartDataSetProtocol> _Nullable)getDataSetByTouchPointWithPoint:(CGPoint)pt SWIFT_WARN_UNUSED_RESULT;
/// The current x-scale factor
@property (nonatomic, readonly) CGFloat scaleX;
/// The current y-scale factor
@property (nonatomic, readonly) CGFloat scaleY;
/// if the chart is fully zoomed out, return true
@property (nonatomic, readonly) BOOL isFullyZoomedOut;
///
/// returns:
/// The y-axis object to the corresponding AxisDependency. In the
/// horizontal bar-chart, LEFT == top, RIGHT == BOTTOM
- (ChartYAxis * _Nonnull)getAxis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
/// flag that indicates if pinch-zoom is enabled. if true, both x and y axis can be scaled simultaneously with 2 fingers, if false, x and y axis can be scaled separately
@property (nonatomic) BOOL pinchZoomEnabled;
/// <em>default</em>: false
/// <code>true</code> if pinch-zoom is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isPinchZoomEnabled;
/// Set an offset in dp that allows the user to drag the chart over it’s
/// bounds on the x-axis.
- (void)setDragOffsetX:(CGFloat)offset;
/// Set an offset in dp that allows the user to drag the chart over it’s
/// bounds on the y-axis.
- (void)setDragOffsetY:(CGFloat)offset;
/// <code>true</code> if both drag offsets (x and y) are zero or smaller.
@property (nonatomic, readonly) BOOL hasNoDragOffset;
@property (nonatomic, readonly) double chartYMax;
@property (nonatomic, readonly) double chartYMin;
/// <code>true</code> if either the left or the right or both axes are inverted.
@property (nonatomic, readonly) BOOL isAnyAxisInverted;
/// flag that indicates if auto scaling on the y axis is enabled.
/// if yes, the y axis automatically adjusts to the min and max y values of the current x axis range whenever the viewport changes
@property (nonatomic) BOOL autoScaleMinMaxEnabled;
/// <em>default</em>: false
/// <code>true</code> if auto scaling on the y axis is enabled.
@property (nonatomic, readonly) BOOL isAutoScaleMinMaxEnabled;
/// Sets a minimum width to the specified y axis.
- (void)setYAxisMinWidth:(enum AxisDependency)axis width:(CGFloat)width;
/// <em>default</em>: 0.0
///
/// returns:
/// The (custom) minimum width of the specified Y axis.
- (CGFloat)getYAxisMinWidth:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
/// Sets a maximum width to the specified y axis.
/// Zero (0.0) means there’s no maximum width
- (void)setYAxisMaxWidth:(enum AxisDependency)axis width:(CGFloat)width;
/// Zero (0.0) means there’s no maximum width
/// <em>default</em>: 0.0 (no maximum specified)
///
/// returns:
/// The (custom) maximum width of the specified Y axis.
- (CGFloat)getYAxisMaxWidth:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
/// <ul>
///   <li>
///     Returns the width of the specified y axis.
///   </li>
/// </ul>
- (CGFloat)getYAxisWidth:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The Transformer class that contains all matrices and is
/// responsible for transforming values into pixels on the screen and
/// backwards.
- (ChartTransformer * _Nonnull)getTransformerForAxis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
/// the number of maximum visible drawn values on the chart only active when <code>drawValuesEnabled</code> is enabled
@property (nonatomic) NSInteger maxVisibleCount;
- (BOOL)isInvertedWithAxis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
/// The lowest x-index (value on the x-axis) that is still visible on he chart.
@property (nonatomic, readonly) double lowestVisibleX;
/// The highest x-index (value on the x-axis) that is still visible on the chart.
@property (nonatomic, readonly) double highestVisibleX;
@end

/// Chart that draws bars.
SWIFT_CLASS("_TtC8DGCharts12BarChartView")
@interface BarChartView : BarLineChartViewBase <BarChartDataProvider>
///
/// returns:
/// The Highlight object (contains x-index and DataSet index) of the selected value at the given touch point inside the BarChart.
- (ChartHighlight * _Nullable)getHighlightByTouchPoint:(CGPoint)pt SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The bounding box of the specified Entry in the specified DataSet. Returns null if the Entry could not be found in the charts data.
- (CGRect)getBarBoundsWithEntry:(BarChartDataEntry * _Nonnull)e SWIFT_WARN_UNUSED_RESULT;
/// Groups all BarDataSet objects this data object holds together by modifying the x-value of their entries.
/// Previously set x-values of entries will be overwritten. Leaves space between bars and groups as specified by the parameters.
/// Calls <code>notifyDataSetChanged()</code> afterwards.
/// \param fromX the starting point on the x-axis where the grouping should begin
///
/// \param groupSpace the space between groups of bars in values (not pixels) e.g. 0.8f for bar width 1f
///
/// \param barSpace the space between individual bars in values (not pixels) e.g. 0.1f for bar width 1f
///
- (void)groupBarsFromX:(double)fromX groupSpace:(double)groupSpace barSpace:(double)barSpace;
/// Highlights the value at the given x-value in the given DataSet. Provide -1 as the dataSetIndex to undo all highlighting.
/// \param x 
///
/// \param dataSetIndex 
///
/// \param stackIndex the index inside the stack - only relevant for stacked entries
///
- (void)highlightValueWithX:(double)x dataSetIndex:(NSInteger)dataSetIndex stackIndex:(NSInteger)stackIndex;
/// if set to true, all values are drawn above their bars, instead of below their top
@property (nonatomic) BOOL drawValueAboveBarEnabled;
/// if set to true, a grey area is drawn behind each bar that indicates the maximum value
@property (nonatomic) BOOL drawBarShadowEnabled;
/// Adds half of the bar width to each side of the x-axis range in order to allow the bars of the barchart to be fully displayed.
/// <em>default</em>: false
@property (nonatomic) BOOL fitBars;
/// Set this to <code>true</code> to make the highlight operation full-bar oriented, <code>false</code> to make it highlight single values (relevant only for stacked).
/// If enabled, highlighting operations will highlight the whole bar, even if only a single stack entry was tapped.
@property (nonatomic) BOOL highlightFullBarEnabled;
/// <code>true</code> the highlight is be full-bar oriented, <code>false</code> ifsingle-value
@property (nonatomic, readonly) BOOL isHighlightFullBarEnabled;
@property (nonatomic, readonly, strong) BarChartData * _Nullable barData;
/// <code>true</code> if drawing values above bars is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawValueAboveBarEnabled;
/// <code>true</code> if drawing shadows (maxvalue) for each bar is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawBarShadowEnabled;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_PROTOCOL_NAMED("Highlighter")
@protocol ChartHighlighter
/// \param x 
///
/// \param y 
///
///
/// returns:
/// A Highlight object corresponding to the given x- and y- touch positions in pixels.
- (ChartHighlight * _Nullable)getHighlightWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_CLASS("_TtC8DGCharts16ChartHighlighter")
@interface ChartHighlighter : NSObject <ChartHighlighter>
/// instance of the data-provider
@property (nonatomic, weak) id <ChartDataProvider> _Nullable chart;
- (nonnull instancetype)initWithChart:(id <ChartDataProvider> _Nonnull)chart OBJC_DESIGNATED_INITIALIZER;
- (ChartHighlight * _Nullable)getHighlightWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// \param x 
///
///
/// returns:
/// The corresponding x-pos for a given touch-position in pixels.
- (CGPoint)getValsForTouchWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// \param xValue 
///
/// \param x 
///
/// \param y 
///
///
/// returns:
/// The corresponding ChartHighlight for a given x-value and xy-touch position in pixels.
- (ChartHighlight * _Nullable)getHighlightWithXValue:(double)xVal x:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// \param xValue the transformed x-value of the x-touch position
///
/// \param x touch position
///
/// \param y touch position
///
///
/// returns:
/// A list of Highlight objects representing the entries closest to the given xVal.
/// The returned list contains two objects per DataSet (closest rounding up, closest rounding down).
- (NSArray<ChartHighlight *> * _Nonnull)getHighlightsWithXValue:(double)xValue x:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS_NAMED("BarHighlighter")
@interface BarChartHighlighter : ChartHighlighter
- (ChartHighlight * _Nullable)getHighlightWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// This method creates the Highlight object that also indicates which value of a stacked BarEntry has been selected.
/// \param high the Highlight to work with looking for stacked values
///
/// \param set 
///
/// \param xIndex 
///
/// \param yValue 
///
///
/// returns:
///
- (ChartHighlight * _Nullable)getStackedHighlightWithHigh:(ChartHighlight * _Nonnull)high set:(id <BarChartDataSetProtocol> _Nonnull)set xValue:(double)xValue yValue:(double)yValue SWIFT_WARN_UNUSED_RESULT;
/// \param entry 
///
/// \param value 
///
///
/// returns:
/// The index of the closest value inside the values array / ranges (stacked barchart) to the value given as a parameter.
- (NSInteger)getClosestStackIndexWithRanges:(NSArray<ChartRange *> * _Nullable)ranges value:(double)value SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithChart:(id <ChartDataProvider> _Nonnull)chart OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts15BubbleChartData")
@interface BubbleChartData : BarLineScatterCandleBubbleChartData
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets OBJC_DESIGNATED_INITIALIZER;
/// Sets the width of the circle that surrounds the bubble when highlighted for all DataSet objects this data object contains
- (void)setHighlightCircleWidth:(CGFloat)width;
@end

SWIFT_CLASS("_TtC8DGCharts20BubbleChartDataEntry")
@interface BubbleChartDataEntry : ChartDataEntry
/// The size of the bubble.
@property (nonatomic) CGFloat size;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// \param x The index on the x-axis.
///
/// \param y The value on the y-axis.
///
/// \param size The size of the bubble.
///
- (nonnull instancetype)initWithX:(double)x y:(double)y size:(CGFloat)size OBJC_DESIGNATED_INITIALIZER;
/// \param x The index on the x-axis.
///
/// \param y The value on the y-axis.
///
/// \param size The size of the bubble.
///
/// \param data Spot for additional data this Entry represents.
///
- (nonnull instancetype)initWithX:(double)x y:(double)y size:(CGFloat)size data:(id _Nullable)data;
/// \param x The index on the x-axis.
///
/// \param y The value on the y-axis.
///
/// \param size The size of the bubble.
///
/// \param icon icon image
///
- (nonnull instancetype)initWithX:(double)x y:(double)y size:(CGFloat)size icon:(UIImage * _Nullable)icon;
/// \param x The index on the x-axis.
///
/// \param y The value on the y-axis.
///
/// \param size The size of the bubble.
///
/// \param icon icon image
///
/// \param data Spot for additional data this Entry represents.
///
- (nonnull instancetype)initWithX:(double)x y:(double)y size:(CGFloat)size icon:(UIImage * _Nullable)icon data:(id _Nullable)data;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithX:(double)x y:(double)y SWIFT_UNAVAILABLE;
@end

SWIFT_PROTOCOL("_TtP8DGCharts23BubbleChartDataProvider_")
@protocol BubbleChartDataProvider <BarLineScatterCandleBubbleChartDataProvider>
@property (nonatomic, readonly, strong) BubbleChartData * _Nullable bubbleData;
@end

SWIFT_PROTOCOL("_TtP8DGCharts26BubbleChartDataSetProtocol_")
@protocol BubbleChartDataSetProtocol <BarLineScatterCandleBubbleChartDataSetProtocol>
@property (nonatomic, readonly) CGFloat maxSize;
@property (nonatomic, readonly) BOOL isNormalizeSizeEnabled;
/// Sets/gets the width of the circle that surrounds the bubble when highlighted
@property (nonatomic) CGFloat highlightCircleWidth;
@end

SWIFT_CLASS("_TtC8DGCharts18BubbleChartDataSet")
@interface BubbleChartDataSet : BarLineScatterCandleBubbleChartDataSet <BubbleChartDataSetProtocol>
@property (nonatomic, readonly) CGFloat maxSize;
@property (nonatomic) BOOL normalizeSizeEnabled;
@property (nonatomic, readonly) BOOL isNormalizeSizeEnabled;
/// Sets/gets the width of the circle that surrounds the bubble when highlighted
@property (nonatomic) CGFloat highlightCircleWidth;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts19BubbleChartRenderer")
@interface BubbleChartRenderer : BarLineScatterCandleBubbleChartRenderer
@property (nonatomic, weak) id <BubbleChartDataProvider> _Nullable dataProvider;
- (nonnull instancetype)initWithDataProvider:(id <BubbleChartDataProvider> _Nonnull)dataProvider animator:(ChartAnimator * _Nonnull)animator viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
- (void)drawDataWithContext:(CGContextRef _Nonnull)context;
- (void)drawDataSetWithContext:(CGContextRef _Nonnull)context dataSet:(id <BubbleChartDataSetProtocol> _Nonnull)dataSet dataSetIndex:(NSInteger)dataSetIndex;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
- (void)drawExtrasWithContext:(CGContextRef _Nonnull)context;
- (void)drawHighlightedWithContext:(CGContextRef _Nonnull)context indices:(NSArray<ChartHighlight *> * _Nonnull)indices;
@end

SWIFT_CLASS("_TtC8DGCharts15BubbleChartView")
@interface BubbleChartView : BarLineChartViewBase <BubbleChartDataProvider>
@property (nonatomic, readonly, strong) BubbleChartData * _Nullable bubbleData;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts15CandleChartData")
@interface CandleChartData : BarLineScatterCandleBubbleChartData
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts20CandleChartDataEntry")
@interface CandleChartDataEntry : ChartDataEntry
/// shadow-high value
@property (nonatomic) double high;
/// shadow-low value
@property (nonatomic) double low;
/// close value
@property (nonatomic) double close;
/// open value
@property (nonatomic) double open;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithX:(double)x shadowH:(double)shadowH shadowL:(double)shadowL open:(double)open close:(double)close OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithX:(double)x shadowH:(double)shadowH shadowL:(double)shadowL open:(double)open close:(double)close icon:(UIImage * _Nullable)icon;
- (nonnull instancetype)initWithX:(double)x shadowH:(double)shadowH shadowL:(double)shadowL open:(double)open close:(double)close data:(id _Nullable)data;
- (nonnull instancetype)initWithX:(double)x shadowH:(double)shadowH shadowL:(double)shadowL open:(double)open close:(double)close icon:(UIImage * _Nullable)icon data:(id _Nullable)data;
/// The overall range (difference) between shadow-high and shadow-low.
@property (nonatomic, readonly) double shadowRange;
/// The body size (difference between open and close).
@property (nonatomic, readonly) double bodyRange;
/// the center value of the candle. (Middle value between high and low)
@property (nonatomic) double y;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithX:(double)x y:(double)y SWIFT_UNAVAILABLE;
@end

SWIFT_PROTOCOL("_TtP8DGCharts23CandleChartDataProvider_")
@protocol CandleChartDataProvider <BarLineScatterCandleBubbleChartDataProvider>
@property (nonatomic, readonly, strong) CandleChartData * _Nullable candleData;
@end

SWIFT_PROTOCOL("_TtP8DGCharts42LineScatterCandleRadarChartDataSetProtocol_")
@protocol LineScatterCandleRadarChartDataSetProtocol <BarLineScatterCandleBubbleChartDataSetProtocol>
/// Enables / disables the horizontal highlight-indicator. If disabled, the indicator is not drawn.
@property (nonatomic) BOOL drawHorizontalHighlightIndicatorEnabled;
/// Enables / disables the vertical highlight-indicator. If disabled, the indicator is not drawn.
@property (nonatomic) BOOL drawVerticalHighlightIndicatorEnabled;
/// <code>true</code> if horizontal highlight indicator lines are enabled (drawn)
@property (nonatomic, readonly) BOOL isHorizontalHighlightIndicatorEnabled;
/// <code>true</code> if vertical highlight indicator lines are enabled (drawn)
@property (nonatomic, readonly) BOOL isVerticalHighlightIndicatorEnabled;
/// Enables / disables both vertical and horizontal highlight-indicators.
/// :param: enabled
- (void)setDrawHighlightIndicators:(BOOL)enabled;
@end

SWIFT_PROTOCOL("_TtP8DGCharts26CandleChartDataSetProtocol_")
@protocol CandleChartDataSetProtocol <LineScatterCandleRadarChartDataSetProtocol>
/// the space that is left out on the left and right side of each candle,
/// <em>default</em>: 0.1 (10%), max 0.45, min 0.0
@property (nonatomic) CGFloat barSpace;
/// should the candle bars show?
/// when false, only “ticks” will show
/// <em>default</em>: true
@property (nonatomic) BOOL showCandleBar;
/// the width of the candle-shadow-line in pixels.
/// <em>default</em>: 3.0
@property (nonatomic) CGFloat shadowWidth;
/// the color of the shadow line
@property (nonatomic, strong) UIColor * _Nullable shadowColor;
/// use candle color for the shadow
@property (nonatomic) BOOL shadowColorSameAsCandle;
/// Is the shadow color same as the candle color?
@property (nonatomic, readonly) BOOL isShadowColorSameAsCandle;
/// color for open == close
@property (nonatomic, strong) UIColor * _Nullable neutralColor;
/// color for open > close
@property (nonatomic, strong) UIColor * _Nullable increasingColor;
/// color for open < close
@property (nonatomic, strong) UIColor * _Nullable decreasingColor;
/// Are increasing values drawn as filled?
@property (nonatomic) BOOL increasingFilled;
/// Are increasing values drawn as filled?
@property (nonatomic, readonly) BOOL isIncreasingFilled;
/// Are decreasing values drawn as filled?
@property (nonatomic) BOOL decreasingFilled;
/// Are decreasing values drawn as filled?
@property (nonatomic, readonly) BOOL isDecreasingFilled;
@end

SWIFT_CLASS("_TtC8DGCharts34LineScatterCandleRadarChartDataSet")
@interface LineScatterCandleRadarChartDataSet : BarLineScatterCandleBubbleChartDataSet <LineScatterCandleRadarChartDataSetProtocol>
/// Enables / disables the horizontal highlight-indicator. If disabled, the indicator is not drawn.
@property (nonatomic) BOOL drawHorizontalHighlightIndicatorEnabled;
/// Enables / disables the vertical highlight-indicator. If disabled, the indicator is not drawn.
@property (nonatomic) BOOL drawVerticalHighlightIndicatorEnabled;
/// <code>true</code> if horizontal highlight indicator lines are enabled (drawn)
@property (nonatomic, readonly) BOOL isHorizontalHighlightIndicatorEnabled;
/// <code>true</code> if vertical highlight indicator lines are enabled (drawn)
@property (nonatomic, readonly) BOOL isVerticalHighlightIndicatorEnabled;
/// Enables / disables both vertical and horizontal highlight-indicators.
/// :param: enabled
- (void)setDrawHighlightIndicators:(BOOL)enabled;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts18CandleChartDataSet")
@interface CandleChartDataSet : LineScatterCandleRadarChartDataSet <CandleChartDataSetProtocol>
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
- (void)calcMinMaxYWithEntry:(ChartDataEntry * _Nonnull)e;
/// the space that is left out on the left and right side of each candle,
/// <em>default</em>: 0.1 (10%), max 0.45, min 0.0
@property (nonatomic) CGFloat barSpace;
/// should the candle bars show?
/// when false, only “ticks” will show
/// <em>default</em>: true
@property (nonatomic) BOOL showCandleBar;
/// the width of the candle-shadow-line in pixels.
/// <em>default</em>: 1.5
@property (nonatomic) CGFloat shadowWidth;
/// the color of the shadow line
@property (nonatomic, strong) UIColor * _Nullable shadowColor;
/// use candle color for the shadow
@property (nonatomic) BOOL shadowColorSameAsCandle;
/// Is the shadow color same as the candle color?
@property (nonatomic, readonly) BOOL isShadowColorSameAsCandle;
/// color for open == close
@property (nonatomic, strong) UIColor * _Nullable neutralColor;
/// color for open > close
@property (nonatomic, strong) UIColor * _Nullable increasingColor;
/// color for open < close
@property (nonatomic, strong) UIColor * _Nullable decreasingColor;
/// Are increasing values drawn as filled?
/// increasing candlesticks are traditionally hollow
@property (nonatomic) BOOL increasingFilled;
/// Are increasing values drawn as filled?
@property (nonatomic, readonly) BOOL isIncreasingFilled;
/// Are decreasing values drawn as filled?
/// descreasing candlesticks are traditionally filled
@property (nonatomic) BOOL decreasingFilled;
/// Are decreasing values drawn as filled?
@property (nonatomic, readonly) BOOL isDecreasingFilled;
@end

SWIFT_CLASS_NAMED("LineScatterCandleRadarRenderer")
@interface LineScatterCandleRadarChartRenderer : BarLineScatterCandleBubbleChartRenderer
/// Draws vertical & horizontal highlight-lines if enabled.
/// :param: context
/// :param: points
/// :param: horizontal
/// :param: vertical
- (void)drawHighlightLinesWithContext:(CGContextRef _Nonnull)context point:(CGPoint)point set:(id <LineScatterCandleRadarChartDataSetProtocol> _Nonnull)set;
@end

SWIFT_CLASS("_TtC8DGCharts24CandleStickChartRenderer")
@interface CandleStickChartRenderer : LineScatterCandleRadarChartRenderer
@property (nonatomic, weak) id <CandleChartDataProvider> _Nullable dataProvider;
- (nonnull instancetype)initWithDataProvider:(id <CandleChartDataProvider> _Nonnull)dataProvider animator:(ChartAnimator * _Nonnull)animator viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
- (void)drawDataWithContext:(CGContextRef _Nonnull)context;
- (void)drawDataSetWithContext:(CGContextRef _Nonnull)context dataSet:(id <CandleChartDataSetProtocol> _Nonnull)dataSet;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
- (void)drawExtrasWithContext:(CGContextRef _Nonnull)context;
- (void)drawHighlightedWithContext:(CGContextRef _Nonnull)context indices:(NSArray<ChartHighlight *> * _Nonnull)indices;
@end

/// Financial chart type that draws candle-sticks.
SWIFT_CLASS("_TtC8DGCharts20CandleStickChartView")
@interface CandleStickChartView : BarLineChartViewBase <CandleChartDataProvider>
@property (nonatomic, readonly, strong) CandleChartData * _Nullable candleData;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts19ChartColorTemplates")
@interface ChartColorTemplates : NSObject
+ (NSArray<UIColor *> * _Nonnull)liberty SWIFT_WARN_UNUSED_RESULT;
+ (NSArray<UIColor *> * _Nonnull)joyful SWIFT_WARN_UNUSED_RESULT;
+ (NSArray<UIColor *> * _Nonnull)pastel SWIFT_WARN_UNUSED_RESULT;
+ (NSArray<UIColor *> * _Nonnull)colorful SWIFT_WARN_UNUSED_RESULT;
+ (NSArray<UIColor *> * _Nonnull)vordiplom SWIFT_WARN_UNUSED_RESULT;
+ (NSArray<UIColor *> * _Nonnull)material SWIFT_WARN_UNUSED_RESULT;
+ (UIColor * _Nonnull)colorFromString:(NSString * _Nonnull)colorString SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@interface ChartData (SWIFT_EXTENSION(DGCharts))
- (void)addDataSet:(id <ChartDataSetProtocol> _Nonnull)newElement;
- (id <ChartDataSetProtocol> _Nonnull)removeDataSetByIndex:(NSInteger)position SWIFT_WARN_UNUSED_RESULT;
@end

@interface ChartDataEntry (SWIFT_EXTENSION(DGCharts))
- (BOOL)isEqual:(id _Nullable)object SWIFT_WARN_UNUSED_RESULT;
@end

@interface ChartDataEntryBase (SWIFT_EXTENSION(DGCharts))
- (BOOL)isEqual:(id _Nullable)object SWIFT_WARN_UNUSED_RESULT;
@end

@interface ChartDataSet (SWIFT_EXTENSION(DGCharts))
- (void)removeAllWithKeepingCapacity:(BOOL)keepCapacity;
@end

@interface ChartDataSet (SWIFT_EXTENSION(DGCharts))
- (ChartDataEntry * _Nonnull)objectAtIndexedSubscript:(NSInteger)position SWIFT_WARN_UNUSED_RESULT;
- (void)setObject:(ChartDataEntry * _Nonnull)newValue atIndexedSubscript:(NSInteger)position;
@end

/// Determines how to round DataSet index values for <code>ChartDataSet.entryIndex(x, rounding)</code> when an exact x-value is not found.
typedef SWIFT_ENUM(NSInteger, ChartDataSetRounding, closed) {
  ChartDataSetRoundingUp = 0,
  ChartDataSetRoundingDown = 1,
  ChartDataSetRoundingClosest = 2,
};

typedef SWIFT_ENUM(NSInteger, ChartEasingOption, closed) {
  ChartEasingOptionLinear = 0,
  ChartEasingOptionEaseInQuad = 1,
  ChartEasingOptionEaseOutQuad = 2,
  ChartEasingOptionEaseInOutQuad = 3,
  ChartEasingOptionEaseInCubic = 4,
  ChartEasingOptionEaseOutCubic = 5,
  ChartEasingOptionEaseInOutCubic = 6,
  ChartEasingOptionEaseInQuart = 7,
  ChartEasingOptionEaseOutQuart = 8,
  ChartEasingOptionEaseInOutQuart = 9,
  ChartEasingOptionEaseInQuint = 10,
  ChartEasingOptionEaseOutQuint = 11,
  ChartEasingOptionEaseInOutQuint = 12,
  ChartEasingOptionEaseInSine = 13,
  ChartEasingOptionEaseOutSine = 14,
  ChartEasingOptionEaseInOutSine = 15,
  ChartEasingOptionEaseInExpo = 16,
  ChartEasingOptionEaseOutExpo = 17,
  ChartEasingOptionEaseInOutExpo = 18,
  ChartEasingOptionEaseInCirc = 19,
  ChartEasingOptionEaseOutCirc = 20,
  ChartEasingOptionEaseInOutCirc = 21,
  ChartEasingOptionEaseInElastic = 22,
  ChartEasingOptionEaseOutElastic = 23,
  ChartEasingOptionEaseInOutElastic = 24,
  ChartEasingOptionEaseInBack = 25,
  ChartEasingOptionEaseOutBack = 26,
  ChartEasingOptionEaseInOutBack = 27,
  ChartEasingOptionEaseInBounce = 28,
  ChartEasingOptionEaseOutBounce = 29,
  ChartEasingOptionEaseInOutBounce = 30,
};

enum ChartLimitLabelPosition : NSInteger;
/// The limit line is an additional feature for all Line, Bar and ScatterCharts.
/// It allows the displaying of an additional line in the chart that marks a certain maximum / limit on the specified axis (x- or y-axis).
SWIFT_CLASS("_TtC8DGCharts14ChartLimitLine")
@interface ChartLimitLine : ChartComponentBase
/// limit / maximum (the y-value or xIndex)
@property (nonatomic) double limit;
@property (nonatomic, strong) UIColor * _Nonnull lineColor;
@property (nonatomic) CGFloat lineDashPhase;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable lineDashLengths;
@property (nonatomic, strong) UIColor * _Nonnull valueTextColor;
@property (nonatomic, strong) UIFont * _Nonnull valueFont;
@property (nonatomic) BOOL drawLabelEnabled;
@property (nonatomic, copy) NSString * _Nonnull label;
@property (nonatomic) enum ChartLimitLabelPosition labelPosition;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithLimit:(double)limit OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithLimit:(double)limit label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
/// set the line width of the chart (min = 0.2, max = 12); default 2
@property (nonatomic) CGFloat lineWidth;
@end

typedef SWIFT_ENUM_NAMED(NSInteger, ChartLimitLabelPosition, "LabelPosition", closed) {
  ChartLimitLabelPositionLeftTop = 0,
  ChartLimitLabelPositionLeftBottom = 1,
  ChartLimitLabelPositionRightTop = 2,
  ChartLimitLabelPositionRightBottom = 3,
};

SWIFT_PROTOCOL("_TtP8DGCharts17ChartViewDelegate_")
@protocol ChartViewDelegate
@optional
/// Called when a value has been selected inside the chart.
/// \param entry The selected Entry.
///
/// \param highlight The corresponding highlight object that contains information about the highlighted position such as dataSetIndex etc.
///
- (void)chartValueSelected:(ChartViewBase * _Nonnull)chartView entry:(ChartDataEntry * _Nonnull)entry highlight:(ChartHighlight * _Nonnull)highlight;
/// Called when a user stops panning between values on the chart
- (void)chartViewDidEndPanning:(ChartViewBase * _Nonnull)chartView;
- (void)chartValueNothingSelected:(ChartViewBase * _Nonnull)chartView;
- (void)chartScaled:(ChartViewBase * _Nonnull)chartView scaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY;
- (void)chartTranslated:(ChartViewBase * _Nonnull)chartView dX:(CGFloat)dX dY:(CGFloat)dY;
- (void)chartView:(ChartViewBase * _Nonnull)chartView animatorDidStop:(ChartAnimator * _Nonnull)animator;
@end

@protocol ScatterChartDataSetProtocol;
SWIFT_PROTOCOL("_TtP8DGCharts13ShapeRenderer_")
@protocol ShapeRenderer
/// Renders the provided ScatterDataSet with a shape.
/// \param context CGContext for drawing on
///
/// \param dataSet The DataSet to be drawn
///
/// \param viewPortHandler Contains information about the current state of the view
///
/// \param point Position to draw the shape at
///
/// \param color Color to draw the shape
///
- (void)renderShapeWithContext:(CGContextRef _Nonnull)context dataSet:(id <ScatterChartDataSetProtocol> _Nonnull)dataSet viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler point:(CGPoint)point color:(UIColor * _Nonnull)color;
@end

SWIFT_CLASS("_TtC8DGCharts24ChevronDownShapeRenderer")
@interface ChevronDownShapeRenderer : NSObject <ShapeRenderer>
- (void)renderShapeWithContext:(CGContextRef _Nonnull)context dataSet:(id <ScatterChartDataSetProtocol> _Nonnull)dataSet viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler point:(CGPoint)point color:(UIColor * _Nonnull)color;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts22ChevronUpShapeRenderer")
@interface ChevronUpShapeRenderer : NSObject <ShapeRenderer>
- (void)renderShapeWithContext:(CGContextRef _Nonnull)context dataSet:(id <ScatterChartDataSetProtocol> _Nonnull)dataSet viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler point:(CGPoint)point color:(UIColor * _Nonnull)color;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts19CircleShapeRenderer")
@interface CircleShapeRenderer : NSObject <ShapeRenderer>
- (void)renderShapeWithContext:(CGContextRef _Nonnull)context dataSet:(id <ScatterChartDataSetProtocol> _Nonnull)dataSet viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler point:(CGPoint)point color:(UIColor * _Nonnull)color;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_PROTOCOL_NAMED("Fill")
@protocol ChartFill
/// Draws the provided path in filled mode with the provided area
- (void)fillPathWithContext:(CGContextRef _Nonnull)context rect:(CGRect)rect;
@end

SWIFT_CLASS_NAMED("ColorFill")
@interface ChartColorFill : NSObject <ChartFill>
@property (nonatomic, readonly) CGColorRef _Nonnull color;
- (nonnull instancetype)initWithCgColor:(CGColorRef _Nonnull)cgColor OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithColor:(UIColor * _Nonnull)color;
- (void)fillPathWithContext:(CGContextRef _Nonnull)context rect:(CGRect)rect;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class LineChartData;
@class ScatterChartData;
SWIFT_CLASS("_TtC8DGCharts17CombinedChartData")
@interface CombinedChartData : BarLineScatterCandleBubbleChartData
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, strong) LineChartData * _Null_unspecified lineData;
@property (nonatomic, strong) BarChartData * _Null_unspecified barData;
@property (nonatomic, strong) ScatterChartData * _Null_unspecified scatterData;
@property (nonatomic, strong) CandleChartData * _Null_unspecified candleData;
@property (nonatomic, strong) BubbleChartData * _Null_unspecified bubbleData;
- (void)calcMinMax;
/// All data objects in row: line-bar-scatter-candle-bubble if not null.
@property (nonatomic, readonly, copy) NSArray<ChartData *> * _Nonnull allData;
- (ChartData * _Nonnull)dataByIndex:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
- (id <ChartDataSetProtocol> _Nullable)removeDataSet:(id <ChartDataSetProtocol> _Nonnull)dataSet SWIFT_WARN_UNUSED_RESULT;
- (BOOL)removeEntry:(ChartDataEntry * _Nonnull)entry dataSetIndex:(NSInteger)dataSetIndex SWIFT_WARN_UNUSED_RESULT;
- (BOOL)removeEntryWithXValue:(double)xValue dataSetIndex:(NSInteger)dataSetIndex SWIFT_WARN_UNUSED_RESULT;
- (void)notifyDataChanged;
/// Get the Entry for a corresponding highlight object
/// \param highlight 
///
///
/// returns:
/// The entry that is highlighted
- (ChartDataEntry * _Nullable)entryFor:(ChartHighlight * _Nonnull)highlight SWIFT_WARN_UNUSED_RESULT;
/// Get dataset for highlight
/// \param highlight current highlight
///
///
/// returns:
/// dataset related to highlight
- (id <ChartDataSetProtocol> _Null_unspecified)getDataSetByHighlight:(ChartHighlight * _Nonnull)highlight SWIFT_WARN_UNUSED_RESULT;
- (void)addDataSet:(id <ChartDataSetProtocol> _Nonnull)newElement;
- (id <ChartDataSetProtocol> _Nonnull)removeDataSetByIndex:(NSInteger)i SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_PROTOCOL("_TtP8DGCharts24ScatterChartDataProvider_")
@protocol ScatterChartDataProvider <BarLineScatterCandleBubbleChartDataProvider>
@property (nonatomic, readonly, strong) ScatterChartData * _Nullable scatterData;
@end

SWIFT_PROTOCOL("_TtP8DGCharts21LineChartDataProvider_")
@protocol LineChartDataProvider <BarLineScatterCandleBubbleChartDataProvider>
@property (nonatomic, readonly, strong) LineChartData * _Nullable lineData;
- (ChartYAxis * _Nonnull)getAxis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_PROTOCOL("_TtP8DGCharts25CombinedChartDataProvider_")
@protocol CombinedChartDataProvider <BarChartDataProvider, BubbleChartDataProvider, CandleChartDataProvider, LineChartDataProvider, ScatterChartDataProvider>
@property (nonatomic, readonly, strong) CombinedChartData * _Nullable combinedData;
@end

@class CombinedChartView;
SWIFT_CLASS("_TtC8DGCharts21CombinedChartRenderer")
@interface CombinedChartRenderer : NSObject <ChartDataRenderer>
@property (nonatomic, readonly, strong) ChartViewPortHandler * _Nonnull viewPortHandler;
@property (nonatomic, copy) NSArray<NSUIAccessibilityElement *> * _Nonnull accessibleChartElements;
@property (nonatomic, readonly, strong) ChartAnimator * _Nonnull animator;
@property (nonatomic, weak) CombinedChartView * _Nullable chart;
/// if set to true, all values are drawn above their bars, instead of below their top
@property (nonatomic) BOOL drawValueAboveBarEnabled;
/// if set to true, a grey area is drawn behind each bar that indicates the maximum value
@property (nonatomic) BOOL drawBarShadowEnabled;
- (nonnull instancetype)initWithChart:(CombinedChartView * _Nonnull)chart animator:(ChartAnimator * _Nonnull)animator viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
- (void)initBuffers SWIFT_METHOD_FAMILY(none);
- (void)drawDataWithContext:(CGContextRef _Nonnull)context;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
- (void)drawExtrasWithContext:(CGContextRef _Nonnull)context;
- (void)drawHighlightedWithContext:(CGContextRef _Nonnull)context indices:(NSArray<ChartHighlight *> * _Nonnull)indices;
- (BOOL)isDrawingValuesAllowedWithDataProvider:(id <ChartDataProvider> _Nullable)dataProvider SWIFT_WARN_UNUSED_RESULT;
/// All sub-renderers.
@property (nonatomic, copy) NSArray<id <ChartDataRenderer>> * _Nonnull subRenderers;
/// <code>true</code> if drawing values above bars is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawValueAboveBarEnabled;
/// <code>true</code> if drawing shadows (maxvalue) for each bar is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawBarShadowEnabled;
- (NSUIAccessibilityElement * _Nonnull)createAccessibleHeaderUsingChart:(ChartViewBase * _Nonnull)chart andData:(ChartData * _Nonnull)data withDefaultDescription:(NSString * _Nonnull)defaultDescription SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@protocol ChartFillFormatter;
/// This chart class allows the combination of lines, bars, scatter and candle data all displayed in one chart area.
SWIFT_CLASS("_TtC8DGCharts17CombinedChartView")
@interface CombinedChartView : BarLineChartViewBase <CombinedChartDataProvider>
@property (nonatomic, strong) ChartData * _Nullable data;
@property (nonatomic, strong) id <ChartFillFormatter> _Nonnull fillFormatter;
///
/// returns:
/// The Highlight object (contains x-index and DataSet index) of the selected value at the given touch point inside the CombinedChart.
- (ChartHighlight * _Nullable)getHighlightByTouchPoint:(CGPoint)pt SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, readonly, strong) CombinedChartData * _Nullable combinedData;
@property (nonatomic, readonly, strong) LineChartData * _Nullable lineData;
@property (nonatomic, readonly, strong) BarChartData * _Nullable barData;
@property (nonatomic, readonly, strong) ScatterChartData * _Nullable scatterData;
@property (nonatomic, readonly, strong) CandleChartData * _Nullable candleData;
@property (nonatomic, readonly, strong) BubbleChartData * _Nullable bubbleData;
/// if set to true, all values are drawn above their bars, instead of below their top
@property (nonatomic) BOOL drawValueAboveBarEnabled;
/// if set to true, a grey area is drawn behind each bar that indicates the maximum value
@property (nonatomic) BOOL drawBarShadowEnabled;
/// <code>true</code> if drawing values above bars is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawValueAboveBarEnabled;
/// <code>true</code> if drawing shadows (maxvalue) for each bar is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawBarShadowEnabled;
/// the order in which the provided data objects should be drawn.
/// The earlier you place them in the provided array, the further they will be in the background.
/// e.g. if you provide [DrawOrder.Bar, DrawOrder.Line], the bars will be drawn behind the lines.
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull drawOrder;
/// Set this to <code>true</code> to make the highlight operation full-bar oriented, <code>false</code> to make it highlight single values
@property (nonatomic) BOOL highlightFullBarEnabled;
/// <code>true</code> the highlight is be full-bar oriented, <code>false</code> ifsingle-value
@property (nonatomic, readonly) BOOL isHighlightFullBarEnabled;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

/// enum that allows to specify the order in which the different data objects for the combined-chart are drawn
typedef SWIFT_ENUM_NAMED(NSInteger, CombinedChartDrawOrder, "DrawOrder", closed) {
  CombinedChartDrawOrderBar = 0,
  CombinedChartDrawOrderBubble = 1,
  CombinedChartDrawOrderLine = 2,
  CombinedChartDrawOrderCandle = 3,
  CombinedChartDrawOrderScatter = 4,
};

SWIFT_CLASS_NAMED("CombinedHighlighter")
@interface CombinedChartHighlighter : ChartHighlighter
- (nonnull instancetype)initWithChart:(id <CombinedChartDataProvider> _Nonnull)chart barDataProvider:(id <BarChartDataProvider> _Nonnull)barDataProvider OBJC_DESIGNATED_INITIALIZER;
- (NSArray<ChartHighlight *> * _Nonnull)getHighlightsWithXValue:(double)xValue x:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithChart:(id <ChartDataProvider> _Nonnull)chart SWIFT_UNAVAILABLE;
@end

SWIFT_CLASS("_TtC8DGCharts18CrossShapeRenderer")
@interface CrossShapeRenderer : NSObject <ShapeRenderer>
- (void)renderShapeWithContext:(CGContextRef _Nonnull)context dataSet:(id <ScatterChartDataSetProtocol> _Nonnull)dataSet viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler point:(CGPoint)point color:(UIColor * _Nonnull)color;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS_NAMED("DataApproximator")
@interface ChartDataApproximator : NSObject
/// uses the douglas peuker algorithm to reduce the given arraylist of entries
+ (NSArray<NSValue *> * _Nonnull)reduceWithDouglasPeuker:(NSArray<NSValue *> * _Nonnull)points tolerance:(CGFloat)tolerance SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@interface ChartDataApproximator (SWIFT_EXTENSION(DGCharts))
/// uses the douglas peuker algorithm to reduce the given arraylist of entries to given number of points
/// More algorithm details here - http://psimpl.sourceforge.net/douglas-peucker.html
+ (NSArray<NSValue *> * _Nonnull)reduceWithDouglasPeukerN:(NSArray<NSValue *> * _Nonnull)points resultCount:(NSInteger)resultCount SWIFT_WARN_UNUSED_RESULT;
@end

@class NSNumberFormatter;
SWIFT_CLASS_NAMED("DefaultAxisValueFormatter")
@interface ChartDefaultAxisValueFormatter : NSObject <ChartAxisValueFormatter>
@property (nonatomic, copy) NSString * _Nonnull (^ _Nullable block)(double, ChartAxisBase * _Nullable);
@property (nonatomic) BOOL hasAutoDecimals;
@property (nonatomic, strong) NSNumberFormatter * _Nullable formatter;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithFormatter:(NSNumberFormatter * _Nonnull)formatter OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDecimals:(NSInteger)decimals OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithBlock:(NSString * _Nonnull (^ _Nonnull)(double, ChartAxisBase * _Nullable))block OBJC_DESIGNATED_INITIALIZER;
+ (ChartDefaultAxisValueFormatter * _Nullable)withBlock:(NSString * _Nonnull (^ _Nonnull)(double, ChartAxisBase * _Nullable))block SWIFT_WARN_UNUSED_RESULT;
- (NSString * _Nonnull)stringForValue:(double)value axis:(ChartAxisBase * _Nullable)axis SWIFT_WARN_UNUSED_RESULT;
@end

@protocol LineChartDataSetProtocol;
/// Protocol for providing a custom logic to where the filling line of a LineDataSet should end. This of course only works if setFillEnabled(…) is set to true.
SWIFT_PROTOCOL_NAMED("FillFormatter")
@protocol ChartFillFormatter
///
/// returns:
/// The vertical (y-axis) position where the filled-line of the LineDataSet should end.
- (CGFloat)getFillLinePositionWithDataSet:(id <LineChartDataSetProtocol> _Nonnull)dataSet dataProvider:(id <LineChartDataProvider> _Nonnull)dataProvider SWIFT_WARN_UNUSED_RESULT;
@end

/// Default formatter that calculates the position of the filled line.
SWIFT_CLASS_NAMED("DefaultFillFormatter")
@interface ChartDefaultFillFormatter : NSObject <ChartFillFormatter>
@property (nonatomic, copy) CGFloat (^ _Nullable block)(id <LineChartDataSetProtocol> _Nonnull, id <LineChartDataProvider> _Nonnull);
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithBlock:(CGFloat (^ _Nonnull)(id <LineChartDataSetProtocol> _Nonnull, id <LineChartDataProvider> _Nonnull))block OBJC_DESIGNATED_INITIALIZER;
+ (ChartDefaultFillFormatter * _Nullable)withBlock:(CGFloat (^ _Nonnull)(id <LineChartDataSetProtocol> _Nonnull, id <LineChartDataProvider> _Nonnull))block SWIFT_WARN_UNUSED_RESULT;
- (CGFloat)getFillLinePositionWithDataSet:(id <LineChartDataSetProtocol> _Nonnull)dataSet dataProvider:(id <LineChartDataProvider> _Nonnull)dataProvider SWIFT_WARN_UNUSED_RESULT;
@end

/// Interface that allows custom formatting of all values inside the chart before they are drawn to the screen.
/// Simply create your own formatting class and let it implement ValueFormatter. Then override the stringForValue()
/// method and return whatever you want.
SWIFT_PROTOCOL_NAMED("ValueFormatter")
@protocol ChartValueFormatter
/// Called when a value (from labels inside the chart) is formatted before being drawn.
/// For performance reasons, avoid excessive calculations and memory allocations inside this method.
/// \param value The value to be formatted
///
/// \param dataSetIndex The index of the DataSet the entry in focus belongs to
///
/// \param viewPortHandler provides information about the current chart state (scale, translation, …)
///
///
/// returns:
/// The formatted label ready to be drawn
- (NSString * _Nonnull)stringForValue:(double)value entry:(ChartDataEntry * _Nonnull)entry dataSetIndex:(NSInteger)dataSetIndex viewPortHandler:(ChartViewPortHandler * _Nullable)viewPortHandler SWIFT_WARN_UNUSED_RESULT;
@end

/// The default value formatter used for all chart components that needs a default
SWIFT_CLASS_NAMED("DefaultValueFormatter")
@interface ChartDefaultValueFormatter : NSObject <ChartValueFormatter>
@property (nonatomic, copy) NSString * _Nonnull (^ _Nullable block)(double, ChartDataEntry * _Nonnull, NSInteger, ChartViewPortHandler * _Nullable);
@property (nonatomic) BOOL hasAutoDecimals;
@property (nonatomic, strong) NSNumberFormatter * _Nullable formatter;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithFormatter:(NSNumberFormatter * _Nonnull)formatter OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDecimals:(NSInteger)decimals OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithBlock:(NSString * _Nonnull (^ _Nonnull)(double, ChartDataEntry * _Nonnull, NSInteger, ChartViewPortHandler * _Nullable))block OBJC_DESIGNATED_INITIALIZER;
/// This function is deprecated - Use <code>init(block:)</code> instead.
+ (ChartDefaultValueFormatter * _Nonnull)withBlock:(NSString * _Nonnull (^ _Nonnull)(double, ChartDataEntry * _Nonnull, NSInteger, ChartViewPortHandler * _Nullable))block SWIFT_WARN_UNUSED_RESULT SWIFT_DEPRECATED_MSG("Use `init(block:)` instead.");
- (NSString * _Nonnull)stringForValue:(double)value entry:(ChartDataEntry * _Nonnull)entry dataSetIndex:(NSInteger)dataSetIndex viewPortHandler:(ChartViewPortHandler * _Nullable)viewPortHandler SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_CLASS_NAMED("Description")
@interface ChartDescription : ChartComponentBase
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// The text to be shown as the description.
@property (nonatomic, copy) NSString * _Nullable text;
/// The text alignment of the description text. Default RIGHT.
@property (nonatomic) NSTextAlignment textAlign;
/// Font object used for drawing the description text.
@property (nonatomic, strong) UIFont * _Nonnull font;
/// Text color used for drawing the description text
@property (nonatomic, strong) UIColor * _Nonnull textColor;
@end

SWIFT_CLASS_NAMED("EmptyFill")
@interface ChartEmptyFill : NSObject <ChartFill>
- (void)fillPathWithContext:(CGContextRef _Nonnull)context rect:(CGRect)rect;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS_NAMED("Highlight")
@interface ChartHighlight : NSObject
/// the index of the data object - in case it refers to more than one
@property (nonatomic) NSInteger dataIndex;
/// the x-position (pixels) on which this highlight object was last drawn
@property (nonatomic) CGFloat drawX;
/// the y-position (pixels) on which this highlight object was last drawn
@property (nonatomic) CGFloat drawY;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// \param x the x-value of the highlighted value
///
/// \param y the y-value of the highlighted value
///
/// \param xPx the x-pixel of the highlighted value
///
/// \param yPx the y-pixel of the highlighted value
///
/// \param dataIndex the index of the Data the highlighted value belongs to
///
/// \param dataSetIndex the index of the DataSet the highlighted value belongs to
///
/// \param stackIndex references which value of a stacked-bar entry has been selected
///
/// \param axis the axis the highlighted value belongs to
///
- (nonnull instancetype)initWithX:(double)x y:(double)y xPx:(CGFloat)xPx yPx:(CGFloat)yPx dataIndex:(NSInteger)dataIndex dataSetIndex:(NSInteger)dataSetIndex stackIndex:(NSInteger)stackIndex axis:(enum AxisDependency)axis OBJC_DESIGNATED_INITIALIZER;
/// \param x the x-value of the highlighted value
///
/// \param y the y-value of the highlighted value
///
/// \param xPx the x-pixel of the highlighted value
///
/// \param yPx the y-pixel of the highlighted value
///
/// \param dataSetIndex the index of the DataSet the highlighted value belongs to
///
/// \param stackIndex references which value of a stacked-bar entry has been selected
///
/// \param axis the axis the highlighted value belongs to
///
- (nonnull instancetype)initWithX:(double)x y:(double)y xPx:(CGFloat)xPx yPx:(CGFloat)yPx dataSetIndex:(NSInteger)dataSetIndex stackIndex:(NSInteger)stackIndex axis:(enum AxisDependency)axis;
/// \param x the x-value of the highlighted value
///
/// \param y the y-value of the highlighted value
///
/// \param xPx the x-pixel of the highlighted value
///
/// \param yPx the y-pixel of the highlighted value
///
/// \param dataIndex the index of the Data the highlighted value belongs to
///
/// \param dataSetIndex the index of the DataSet the highlighted value belongs to
///
/// \param stackIndex references which value of a stacked-bar entry has been selected
///
/// \param axis the axis the highlighted value belongs to
///
- (nonnull instancetype)initWithX:(double)x y:(double)y xPx:(CGFloat)xPx yPx:(CGFloat)yPx dataSetIndex:(NSInteger)dataSetIndex axis:(enum AxisDependency)axis OBJC_DESIGNATED_INITIALIZER;
/// \param x the x-value of the highlighted value
///
/// \param y the y-value of the highlighted value
///
/// \param dataSetIndex the index of the DataSet the highlighted value belongs to
///
/// \param dataIndex The data index to search in (only used in CombinedChartView currently)
///
- (nonnull instancetype)initWithX:(double)x y:(double)y dataSetIndex:(NSInteger)dataSetIndex dataIndex:(NSInteger)dataIndex OBJC_DESIGNATED_INITIALIZER;
/// \param x the x-value of the highlighted value
///
/// \param dataSetIndex the index of the DataSet the highlighted value belongs to
///
/// \param stackIndex references which value of a stacked-bar entry has been selected
///
- (nonnull instancetype)initWithX:(double)x dataSetIndex:(NSInteger)dataSetIndex stackIndex:(NSInteger)stackIndex;
@property (nonatomic, readonly) double x;
@property (nonatomic, readonly) double y;
@property (nonatomic, readonly) CGFloat xPx;
@property (nonatomic, readonly) CGFloat yPx;
@property (nonatomic, readonly) NSInteger dataSetIndex;
@property (nonatomic, readonly) NSInteger stackIndex;
@property (nonatomic, readonly) enum AxisDependency axis;
@property (nonatomic, readonly) BOOL isStacked;
/// Sets the x- and y-position (pixels) where this highlight was last drawn.
- (void)setDrawWithX:(CGFloat)x y:(CGFloat)y;
/// Sets the x- and y-position (pixels) where this highlight was last drawn.
- (void)setDrawWithPt:(CGPoint)pt;
@property (nonatomic, readonly, copy) NSString * _Nonnull description;
@end

@interface ChartHighlight (SWIFT_EXTENSION(DGCharts))
- (BOOL)isEqual:(id _Nullable)object SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_CLASS("_TtC8DGCharts26HorizontalBarChartRenderer")
@interface HorizontalBarChartRenderer : BarChartRenderer
- (nonnull instancetype)initWithDataProvider:(id <BarChartDataProvider> _Nonnull)dataProvider animator:(ChartAnimator * _Nonnull)animator viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
- (void)initBuffers SWIFT_METHOD_FAMILY(none);
- (void)drawDataSetWithContext:(CGContextRef _Nonnull)context dataSet:(id <BarChartDataSetProtocol> _Nonnull)dataSet index:(NSInteger)index;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
- (BOOL)isDrawingValuesAllowedWithDataProvider:(id <ChartDataProvider> _Nullable)dataProvider SWIFT_WARN_UNUSED_RESULT;
@end

/// BarChart with horizontal bar orientation. In this implementation, x- and y-axis are switched.
SWIFT_CLASS("_TtC8DGCharts22HorizontalBarChartView")
@interface HorizontalBarChartView : BarChartView
- (CGPoint)getMarkerPositionWithHighlight:(ChartHighlight * _Nonnull)highlight SWIFT_WARN_UNUSED_RESULT;
- (CGRect)getBarBoundsWithEntry:(BarChartDataEntry * _Nonnull)e SWIFT_WARN_UNUSED_RESULT;
- (CGPoint)getPositionWithEntry:(ChartDataEntry * _Nonnull)e axis:(enum AxisDependency)axis SWIFT_WARN_UNUSED_RESULT;
- (ChartHighlight * _Nullable)getHighlightByTouchPoint:(CGPoint)pt SWIFT_WARN_UNUSED_RESULT;
/// The lowest x-index (value on the x-axis) that is still visible on he chart.
@property (nonatomic, readonly) double lowestVisibleX;
/// The highest x-index (value on the x-axis) that is still visible on the chart.
@property (nonatomic, readonly) double highestVisibleX;
- (void)setVisibleXRangeMaximum:(double)maxXRange;
- (void)setVisibleXRangeMinimum:(double)minXRange;
- (void)setVisibleXRangeWithMinXRange:(double)minXRange maxXRange:(double)maxXRange;
- (void)setVisibleYRangeMaximum:(double)maxYRange axis:(enum AxisDependency)axis;
- (void)setVisibleYRangeMinimum:(double)minYRange axis:(enum AxisDependency)axis;
- (void)setVisibleYRangeWithMinYRange:(double)minYRange maxYRange:(double)maxYRange axis:(enum AxisDependency)axis;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS_NAMED("HorizontalBarHighlighter")
@interface HorizontalBarChartHighlighter : BarChartHighlighter
- (ChartHighlight * _Nullable)getHighlightWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithChart:(id <ChartDataProvider> _Nonnull)chart OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS_NAMED("ImageFill")
@interface ChartImageFill : NSObject <ChartFill>
@property (nonatomic, readonly) CGImageRef _Nonnull image;
@property (nonatomic, readonly) BOOL isTiled;
- (nonnull instancetype)initWithCgImage:(CGImageRef _Nonnull)cgImage isTiled:(BOOL)isTiled OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithImage:(UIImage * _Nonnull)image isTiled:(BOOL)isTiled;
- (void)fillPathWithContext:(CGContextRef _Nonnull)context rect:(CGRect)rect;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// This formatter is used for passing an array of x-axis labels, on whole x steps.
SWIFT_CLASS_NAMED("IndexAxisValueFormatter")
@interface ChartIndexAxisValueFormatter : NSObject <ChartAxisValueFormatter>
@property (nonatomic, copy) NSArray<NSString *> * _Nonnull values;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithValues:(NSArray<NSString *> * _Nonnull)values OBJC_DESIGNATED_INITIALIZER;
+ (ChartIndexAxisValueFormatter * _Nullable)withValues:(NSArray<NSString *> * _Nonnull)values SWIFT_WARN_UNUSED_RESULT;
- (NSString * _Nonnull)stringForValue:(double)value axis:(ChartAxisBase * _Nullable)axis SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_CLASS_NAMED("LayerFill")
@interface ChartLayerFill : NSObject <ChartFill>
@property (nonatomic, readonly) CGLayerRef _Nonnull layer;
- (nonnull instancetype)initWithLayer:(CGLayerRef _Nonnull)layer OBJC_DESIGNATED_INITIALIZER;
- (void)fillPathWithContext:(CGContextRef _Nonnull)context rect:(CGRect)rect;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class ChartLegendEntry;
enum ChartLegendHorizontalAlignment : NSInteger;
enum ChartLegendVerticalAlignment : NSInteger;
enum ChartLegendOrientation : NSInteger;
enum ChartLegendDirection : NSInteger;
SWIFT_CLASS_NAMED("Legend")
@interface ChartLegend : ChartComponentBase
/// The legend entries array
@property (nonatomic, copy) NSArray<ChartLegendEntry *> * _Nonnull entries;
/// Entries that will be appended to the end of the auto calculated entries after calculating the legend.
/// (if the legend has already been calculated, you will need to call notifyDataSetChanged() to let the changes take effect)
@property (nonatomic, copy) NSArray<ChartLegendEntry *> * _Nonnull extraEntries;
/// The horizontal alignment of the legend
@property (nonatomic) enum ChartLegendHorizontalAlignment horizontalAlignment;
/// The vertical alignment of the legend
@property (nonatomic) enum ChartLegendVerticalAlignment verticalAlignment;
/// The orientation of the legend
@property (nonatomic) enum ChartLegendOrientation orientation;
/// Flag indicating whether the legend will draw inside the chart or outside
@property (nonatomic) BOOL drawInside;
/// Flag indicating whether the legend will draw inside the chart or outside
@property (nonatomic, readonly) BOOL isDrawInsideEnabled;
/// The text direction of the legend
@property (nonatomic) enum ChartLegendDirection direction;
@property (nonatomic, strong) UIFont * _Nonnull font;
@property (nonatomic, strong) UIColor * _Nonnull textColor;
/// The form/shape of the legend forms
@property (nonatomic) enum ChartLegendForm form;
/// The size of the legend forms
@property (nonatomic) CGFloat formSize;
/// The line width for forms that consist of lines
@property (nonatomic) CGFloat formLineWidth;
/// Line dash configuration for shapes that consist of lines.
/// This is how much (in pixels) into the dash pattern are we starting from.
@property (nonatomic) CGFloat formLineDashPhase;
/// Line dash configuration for shapes that consist of lines.
/// This is the actual dash pattern.
/// I.e. [2, 3] will paint [–   –   ]
/// [1, 3, 4, 2] will paint [-   ––  -   ––  ]
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable formLineDashLengths;
@property (nonatomic) CGFloat xEntrySpace;
@property (nonatomic) CGFloat yEntrySpace;
@property (nonatomic) CGFloat formToTextSpace;
@property (nonatomic) CGFloat stackSpace;
@property (nonatomic, copy) NSArray<NSValue *> * _Nonnull calculatedLabelSizes;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nonnull calculatedLabelBreakPoints;
@property (nonatomic, copy) NSArray<NSValue *> * _Nonnull calculatedLineSizes;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartLegendEntry *> * _Nonnull)entries OBJC_DESIGNATED_INITIALIZER;
- (CGSize)getMaximumEntrySizeWithFont:(UIFont * _Nonnull)font SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic) CGFloat neededWidth;
@property (nonatomic) CGFloat neededHeight;
@property (nonatomic) CGFloat textWidthMax;
@property (nonatomic) CGFloat textHeightMax;
/// flag that indicates if word wrapping is enabled
/// this is currently supported only for <code>orientation == Horizontal</code>.
/// you may want to set maxSizePercent when word wrapping, to set the point where the text wraps.
/// <em>default</em>: true
@property (nonatomic) BOOL wordWrapEnabled;
/// if this is set, then word wrapping the legend is enabled.
@property (nonatomic, readonly) BOOL isWordWrapEnabled;
/// The maximum relative size out of the whole chart view in percent.
/// If the legend is to the right/left of the chart, then this affects the width of the legend.
/// If the legend is to the top/bottom of the chart, then this affects the height of the legend.
/// <em>default</em>: 0.95 (95%)
@property (nonatomic) CGFloat maxSizePercent;
- (void)calculateDimensionsWithLabelFont:(UIFont * _Nonnull)labelFont viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler;
/// Sets a custom legend’s entries array.
/// <ul>
///   <li>
///     A nil label will start a group.
///     This will disable the feature that automatically calculates the legend entries from the datasets.
///     Call <code>resetCustom(...)</code> to re-enable automatic calculation (and then <code>notifyDataSetChanged()</code> is needed).
///   </li>
/// </ul>
- (void)setCustomWithEntries:(NSArray<ChartLegendEntry *> * _Nonnull)entries;
/// Calling this will disable the custom legend entries (set by <code>setLegend(...)</code>). Instead, the entries will again be calculated automatically (after <code>notifyDataSetChanged()</code> is called).
- (void)resetCustom;
/// <em>default</em>: false (automatic legend)
/// <code>true</code> if a custom legend entries has been set
@property (nonatomic, readonly) BOOL isLegendCustom;
@end

typedef SWIFT_ENUM_NAMED(NSInteger, ChartLegendForm, "Form", closed) {
/// Avoid drawing a form
  ChartLegendFormNone = 0,
/// Do not draw the a form, but leave space for it
  ChartLegendFormEmpty = 1,
/// Use default (default dataset’s form to the legend’s form)
  ChartLegendFormDefault = 2,
/// Draw a square
  ChartLegendFormSquare = 3,
/// Draw a circle
  ChartLegendFormCircle = 4,
/// Draw a horizontal line
  ChartLegendFormLine = 5,
};

typedef SWIFT_ENUM_NAMED(NSInteger, ChartLegendHorizontalAlignment, "HorizontalAlignment", closed) {
  ChartLegendHorizontalAlignmentLeft = 0,
  ChartLegendHorizontalAlignmentCenter = 1,
  ChartLegendHorizontalAlignmentRight = 2,
};

typedef SWIFT_ENUM_NAMED(NSInteger, ChartLegendVerticalAlignment, "VerticalAlignment", closed) {
  ChartLegendVerticalAlignmentTop = 0,
  ChartLegendVerticalAlignmentCenter = 1,
  ChartLegendVerticalAlignmentBottom = 2,
};

typedef SWIFT_ENUM_NAMED(NSInteger, ChartLegendOrientation, "Orientation", closed) {
  ChartLegendOrientationHorizontal = 0,
  ChartLegendOrientationVertical = 1,
};

typedef SWIFT_ENUM_NAMED(NSInteger, ChartLegendDirection, "Direction", closed) {
  ChartLegendDirectionLeftToRight = 0,
  ChartLegendDirectionRightToLeft = 1,
};

SWIFT_CLASS_NAMED("LegendEntry")
@interface ChartLegendEntry : NSObject
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// \param label The legend entry text.
/// A <code>nil</code> label will start a group.
///
- (nonnull instancetype)initWithLabel:(NSString * _Nullable)label OBJC_DESIGNATED_INITIALIZER;
/// The legend entry text.
/// A <code>nil</code> label will start a group.
@property (nonatomic, copy) NSString * _Nullable label;
/// The color for drawing the label
@property (nonatomic, strong) UIColor * _Nullable labelColor;
/// The form to draw for this entry.
/// <code>None</code> will avoid drawing a form, and any related space.
/// <code>Empty</code> will avoid drawing a form, but keep its space.
/// <code>Default</code> will use the Legend’s default.
@property (nonatomic) enum ChartLegendForm form;
/// Form size will be considered except for when .None is used
/// Set as NaN to use the legend’s default
@property (nonatomic) CGFloat formSize;
/// Line width used for shapes that consist of lines.
/// Set to NaN to use the legend’s default.
@property (nonatomic) CGFloat formLineWidth;
/// Line dash configuration for shapes that consist of lines.
/// This is how much (in pixels) into the dash pattern are we starting from.
/// Set to NaN to use the legend’s default.
@property (nonatomic) CGFloat formLineDashPhase;
/// Line dash configuration for shapes that consist of lines.
/// This is the actual dash pattern.
/// I.e. [2, 3] will paint [–   –   ]
/// [1, 3, 4, 2] will paint [-   ––  -   ––  ]
/// Set to nil to use the legend’s default.
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable formLineDashLengths;
/// The color for drawing the form
@property (nonatomic, strong) UIColor * _Nullable formColor;
@end

SWIFT_CLASS_NAMED("LegendRenderer")
@interface ChartLegendRenderer : NSObject <ChartRenderer>
@property (nonatomic, readonly, strong) ChartViewPortHandler * _Nonnull viewPortHandler;
/// the legend object this renderer renders
@property (nonatomic, strong) ChartLegend * _Nullable legend;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler legend:(ChartLegend * _Nullable)legend OBJC_DESIGNATED_INITIALIZER;
/// Prepares the legend and calculates all needed forms, labels and colors.
- (void)computeLegendWithData:(ChartData * _Nonnull)data;
- (void)renderLegendWithContext:(CGContextRef _Nonnull)context;
/// Draws the Legend-form at the given position with the color at the given index.
- (void)drawFormWithContext:(CGContextRef _Nonnull)context x:(CGFloat)x y:(CGFloat)y entry:(ChartLegendEntry * _Nonnull)entry legend:(ChartLegend * _Nonnull)legend;
/// Draws the provided label at the given position.
- (void)drawLabelWithContext:(CGContextRef _Nonnull)context x:(CGFloat)x y:(CGFloat)y label:(NSString * _Nonnull)label font:(UIFont * _Nonnull)font textColor:(UIColor * _Nonnull)textColor;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Data object that encapsulates all data associated with a LineChart.
SWIFT_CLASS("_TtC8DGCharts13LineChartData")
@interface LineChartData : ChartData
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_PROTOCOL("_TtP8DGCharts29LineRadarChartDataSetProtocol_")
@protocol LineRadarChartDataSetProtocol <LineScatterCandleRadarChartDataSetProtocol>
/// The color that is used for filling the line surface area.
@property (nonatomic, strong) UIColor * _Nonnull fillColor;
///
/// returns:
/// The object that is used for filling the area below the line.
/// <em>default</em>: nil
@property (nonatomic, strong) id <ChartFill> _Nullable fill;
/// The alpha value that is used for filling the line surface.
/// <em>default</em>: 0.33
@property (nonatomic) CGFloat fillAlpha;
/// line width of the chart (min = 0.0, max = 10)
/// <em>default</em>: 1
@property (nonatomic) CGFloat lineWidth;
/// Set to <code>true</code> if the DataSet should be drawn filled (surface), and not just as a line.
/// Disabling this will give great performance boost.
/// Please note that this method uses the path clipping for drawing the filled area (with images, gradients and layers).
@property (nonatomic) BOOL drawFilledEnabled;
/// <code>true</code> if filled drawing is enabled, <code>false</code> if not
@property (nonatomic, readonly) BOOL isDrawFilledEnabled;
@end

enum LineChartMode : NSInteger;
SWIFT_PROTOCOL("_TtP8DGCharts24LineChartDataSetProtocol_")
@protocol LineChartDataSetProtocol <LineRadarChartDataSetProtocol>
/// The drawing mode for this line dataset
/// <em>default</em>: Linear
@property (nonatomic) enum LineChartMode mode;
/// Intensity for cubic lines (min = 0.05, max = 1)
/// <em>default</em>: 0.2
@property (nonatomic) CGFloat cubicIntensity;
/// If true, gradient lines are drawn instead of solid
@property (nonatomic) BOOL isDrawLineWithGradientEnabled;
/// The points where gradient should change color
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable gradientPositions;
/// The radius of the drawn circles.
@property (nonatomic) CGFloat circleRadius;
/// The hole radius of the drawn circles.
@property (nonatomic) CGFloat circleHoleRadius;
@property (nonatomic, copy) NSArray<UIColor *> * _Nonnull circleColors;
///
/// returns:
/// The color at the given index of the DataSet’s circle-color array.
/// Performs a IndexOutOfBounds check by modulus.
- (UIColor * _Nullable)getCircleColorAtIndex:(NSInteger)atIndex SWIFT_WARN_UNUSED_RESULT;
/// Sets the one and ONLY color that should be used for this DataSet.
/// Internally, this recreates the colors array and adds the specified color.
- (void)setCircleColor:(UIColor * _Nonnull)color;
/// Resets the circle-colors array and creates a new one
- (void)resetCircleColors:(NSInteger)index;
/// If true, drawing circles is enabled
@property (nonatomic) BOOL drawCirclesEnabled;
/// <code>true</code> if drawing circles for this DataSet is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawCirclesEnabled;
/// The color of the inner circle (the circle-hole).
@property (nonatomic, strong) UIColor * _Nullable circleHoleColor;
/// <code>true</code> if drawing circles for this DataSet is enabled, <code>false</code> ifnot
@property (nonatomic) BOOL drawCircleHoleEnabled;
/// <code>true</code> if drawing the circle-holes is enabled, <code>false</code> ifnot.
@property (nonatomic, readonly) BOOL isDrawCircleHoleEnabled;
/// This is how much (in pixels) into the dash pattern are we starting from.
@property (nonatomic, readonly) CGFloat lineDashPhase;
/// This is the actual dash pattern.
/// I.e. [2, 3] will paint [–   –   ]
/// [1, 3, 4, 2] will paint [-   ––  -   ––  ]
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable lineDashLengths;
/// Line cap type, default is CGLineCap.Butt
@property (nonatomic) CGLineCap lineCapType;
/// Sets a custom FillFormatterProtocol to the chart that handles the position of the filled-line for each DataSet. Set this to null to use the default logic.
@property (nonatomic, strong) id <ChartFillFormatter> _Nullable fillFormatter;
@end

SWIFT_CLASS("_TtC8DGCharts21LineRadarChartDataSet")
@interface LineRadarChartDataSet : LineScatterCandleRadarChartDataSet <LineRadarChartDataSetProtocol>
/// The color that is used for filling the line surface area.
@property (nonatomic, strong) UIColor * _Nonnull fillColor;
/// The object that is used for filling the area below the line.
/// <em>default</em>: nil
@property (nonatomic, strong) id <ChartFill> _Nullable fill;
/// The alpha value that is used for filling the line surface,
/// <em>default</em>: 0.33
@property (nonatomic) CGFloat fillAlpha;
/// line width of the chart (min = 0.0, max = 10)
/// <em>default</em>: 1
@property (nonatomic) CGFloat lineWidth;
/// Set to <code>true</code> if the DataSet should be drawn filled (surface), and not just as a line.
/// Disabling this will give great performance boost.
/// Please note that this method uses the path clipping for drawing the filled area (with images, gradients and layers).
@property (nonatomic) BOOL drawFilledEnabled;
/// <code>true</code> if filled drawing is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawFilledEnabled;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts16LineChartDataSet")
@interface LineChartDataSet : LineRadarChartDataSet <LineChartDataSetProtocol>
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
/// The drawing mode for this line dataset
/// <em>default</em>: Linear
@property (nonatomic) enum LineChartMode mode;
/// Intensity for cubic lines (min = 0.05, max = 1)
/// <em>default</em>: 0.2
@property (nonatomic) CGFloat cubicIntensity;
@property (nonatomic) BOOL isDrawLineWithGradientEnabled;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable gradientPositions;
/// The radius of the drawn circles.
@property (nonatomic) CGFloat circleRadius;
/// The hole radius of the drawn circles
@property (nonatomic) CGFloat circleHoleRadius;
@property (nonatomic, copy) NSArray<UIColor *> * _Nonnull circleColors;
///
/// returns:
/// The color at the given index of the DataSet’s circle-color array.
/// Performs a IndexOutOfBounds check by modulus.
- (UIColor * _Nullable)getCircleColorAtIndex:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
/// Sets the one and ONLY color that should be used for this DataSet.
/// Internally, this recreates the colors array and adds the specified color.
- (void)setCircleColor:(UIColor * _Nonnull)color;
/// Resets the circle-colors array and creates a new one
- (void)resetCircleColors:(NSInteger)index;
/// If true, drawing circles is enabled
@property (nonatomic) BOOL drawCirclesEnabled;
/// <code>true</code> if drawing circles for this DataSet is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawCirclesEnabled;
/// The color of the inner circle (the circle-hole).
@property (nonatomic, strong) UIColor * _Nullable circleHoleColor;
/// <code>true</code> if drawing circles for this DataSet is enabled, <code>false</code> ifnot
@property (nonatomic) BOOL drawCircleHoleEnabled;
/// <code>true</code> if drawing the circle-holes is enabled, <code>false</code> ifnot.
@property (nonatomic, readonly) BOOL isDrawCircleHoleEnabled;
/// This is how much (in pixels) into the dash pattern are we starting from.
@property (nonatomic) CGFloat lineDashPhase;
/// This is the actual dash pattern.
/// I.e. [2, 3] will paint [–   –   ]
/// [1, 3, 4, 2] will paint [-   ––  -   ––  ]
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable lineDashLengths;
/// Line cap type, default is CGLineCap.Butt
@property (nonatomic) CGLineCap lineCapType;
/// Sets a custom FillFormatterProtocol to the chart that handles the position of the filled-line for each DataSet. Set this to null to use the default logic.
@property (nonatomic, strong) id <ChartFillFormatter> _Nullable fillFormatter;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
@end

typedef SWIFT_ENUM_NAMED(NSInteger, LineChartMode, "Mode", closed) {
  LineChartModeLinear = 0,
  LineChartModeStepped = 1,
  LineChartModeCubicBezier = 2,
  LineChartModeHorizontalBezier = 3,
};

SWIFT_CLASS_NAMED("LineRadarRenderer")
@interface LineRadarChartRenderer : LineScatterCandleRadarChartRenderer
/// Draws the provided path in filled mode with the provided drawable.
- (void)drawFilledPathWithContext:(CGContextRef _Nonnull)context path:(CGPathRef _Nonnull)path fill:(id <ChartFill> _Nonnull)fill fillAlpha:(CGFloat)fillAlpha;
/// Draws the provided path in filled mode with the provided color and alpha.
- (void)drawFilledPathWithContext:(CGContextRef _Nonnull)context path:(CGPathRef _Nonnull)path fillColor:(UIColor * _Nonnull)fillColor fillAlpha:(CGFloat)fillAlpha;
@end

SWIFT_CLASS("_TtC8DGCharts17LineChartRenderer")
@interface LineChartRenderer : LineRadarChartRenderer
@property (nonatomic, weak) id <LineChartDataProvider> _Nullable dataProvider;
- (nonnull instancetype)initWithDataProvider:(id <LineChartDataProvider> _Nonnull)dataProvider animator:(ChartAnimator * _Nonnull)animator viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
- (void)drawDataWithContext:(CGContextRef _Nonnull)context;
- (void)drawDataSetWithContext:(CGContextRef _Nonnull)context dataSet:(id <LineChartDataSetProtocol> _Nonnull)dataSet;
- (void)drawCubicBezierWithContext:(CGContextRef _Nonnull)context dataSet:(id <LineChartDataSetProtocol> _Nonnull)dataSet;
- (void)drawHorizontalBezierWithContext:(CGContextRef _Nonnull)context dataSet:(id <LineChartDataSetProtocol> _Nonnull)dataSet;
- (void)drawLinearWithContext:(CGContextRef _Nonnull)context dataSet:(id <LineChartDataSetProtocol> _Nonnull)dataSet;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
- (void)drawExtrasWithContext:(CGContextRef _Nonnull)context;
- (void)drawHighlightedWithContext:(CGContextRef _Nonnull)context indices:(NSArray<ChartHighlight *> * _Nonnull)indices;
@end

/// Chart that draws lines, surfaces, circles, …
SWIFT_CLASS("_TtC8DGCharts13LineChartView")
@interface LineChartView : BarLineChartViewBase <LineChartDataProvider>
@property (nonatomic, readonly, strong) LineChartData * _Nullable lineData;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS_NAMED("LinearGradientFill")
@interface ChartLinearGradientFill : NSObject <ChartFill>
@property (nonatomic, readonly) CGGradientRef _Nonnull gradient;
@property (nonatomic, readonly) CGFloat angle;
- (nonnull instancetype)initWithGradient:(CGGradientRef _Nonnull)gradient angle:(CGFloat)angle OBJC_DESIGNATED_INITIALIZER;
- (void)fillPathWithContext:(CGContextRef _Nonnull)context rect:(CGRect)rect;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_PROTOCOL_NAMED("Marker")
@protocol ChartMarker
///
/// returns:
/// The desired (general) offset you wish the IMarker to have on the x-axis.
/// By returning x: -(width / 2) you will center the IMarker horizontally.
/// By returning y: -(height / 2) you will center the IMarker vertically.
@property (nonatomic, readonly) CGPoint offset;
/// \param point This is the point at which the marker wants to be drawn. You can adjust the offset conditionally based on this argument.
///
///
/// returns:
/// The offset for drawing at the specific <code>point</code>.
/// This allows conditional adjusting of the Marker position.
/// If you have no adjustments to make, return self.offset().
- (CGPoint)offsetForDrawingAtPoint:(CGPoint)atPoint SWIFT_WARN_UNUSED_RESULT;
/// This method enables a custom Marker to update it’s content every time the Marker is redrawn according to the data entry it points to.
/// \param entry The Entry the IMarker belongs to. This can also be any subclass of Entry, like BarEntry or CandleEntry, simply cast it at runtime.
///
/// \param highlight The highlight object contains information about the highlighted value such as it’s dataset-index, the selected range or stack-index (only stacked bar entries).
///
- (void)refreshContentWithEntry:(ChartDataEntry * _Nonnull)entry highlight:(ChartHighlight * _Nonnull)highlight;
/// Draws the Marker on the given position on the given context
- (void)drawWithContext:(CGContextRef _Nonnull)context point:(CGPoint)point;
@end

SWIFT_CLASS_NAMED("MarkerImage")
@interface ChartMarkerImage : NSObject <ChartMarker>
/// The marker image to render
@property (nonatomic, strong) UIImage * _Nullable image;
@property (nonatomic) CGPoint offset;
@property (nonatomic, weak) ChartViewBase * _Nullable chartView;
/// As long as size is 0.0/0.0 - it will default to the image’s size
@property (nonatomic) CGSize size;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (CGPoint)offsetForDrawingAtPoint:(CGPoint)point SWIFT_WARN_UNUSED_RESULT;
- (void)refreshContentWithEntry:(ChartDataEntry * _Nonnull)entry highlight:(ChartHighlight * _Nonnull)highlight;
- (void)drawWithContext:(CGContextRef _Nonnull)context point:(CGPoint)point;
@end

@class NSBundle;
SWIFT_CLASS_NAMED("MarkerView")
@interface ChartMarkerView : NSUIView <ChartMarker>
@property (nonatomic) CGPoint offset;
@property (nonatomic, weak) ChartViewBase * _Nullable chartView;
- (CGPoint)offsetForDrawingAtPoint:(CGPoint)point SWIFT_WARN_UNUSED_RESULT;
- (void)refreshContentWithEntry:(ChartDataEntry * _Nonnull)entry highlight:(ChartHighlight * _Nonnull)highlight;
- (void)drawWithContext:(CGContextRef _Nonnull)context point:(CGPoint)point;
+ (ChartMarkerView * _Nullable)viewFromXibIn:(NSBundle * _Nonnull)bundle SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)coder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS_NAMED("MoveViewJob")
@interface MoveChartViewJob : ChartViewPortJob
- (void)doJob;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler xValue:(double)xValue yValue:(double)yValue transformer:(ChartTransformer * _Nonnull)transformer view:(ChartViewBase * _Nonnull)view OBJC_DESIGNATED_INITIALIZER;
@end

/// A simple abstraction over UIAccessibilityElement and NSAccessibilityElement.
SWIFT_CLASS("_TtC8DGCharts24NSUIAccessibilityElement")
@interface NSUIAccessibilityElement : UIAccessibilityElement
- (nonnull instancetype)initWithAccessibilityContainer:(id _Nonnull)container OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic) CGRect accessibilityFrame;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@interface NSUIView (SWIFT_EXTENSION(DGCharts))
/// An array of accessibilityElements that is used to implement UIAccessibilityContainer internally.
/// Subclasses <em>MUST</em> override this with an array of such elements.
- (NSArray * _Nullable)accessibilityChildren SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic) BOOL isAccessibilityElement;
- (NSInteger)accessibilityElementCount SWIFT_WARN_UNUSED_RESULT;
- (id _Nullable)accessibilityElementAtIndex:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
- (NSInteger)indexOfAccessibilityElement:(id _Nonnull)element SWIFT_WARN_UNUSED_RESULT;
@end

@interface NSUIView (SWIFT_EXTENSION(DGCharts))
- (void)touchesBegan:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)touchesMoved:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)touchesEnded:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)touchesCancelled:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)nsuiTouchesBegan:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)nsuiTouchesMoved:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)nsuiTouchesEnded:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)nsuiTouchesCancelled:(NSSet<UITouch *> * _Nullable)touches withEvent:(UIEvent * _Nullable)event;
@end

@protocol PieChartDataSetProtocol;
SWIFT_CLASS("_TtC8DGCharts12PieChartData")
@interface PieChartData : ChartData
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, strong) id <PieChartDataSetProtocol> _Nullable dataSet;
///
/// returns:
/// All up to one dataSet object this ChartData object holds.
@property (nonatomic, copy) NSArray<id <ChartDataSetProtocol>> * _Nonnull dataSets;
- (id <ChartDataSetProtocol> _Nullable)dataSetAtIndex:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
- (id <ChartDataSetProtocol> _Nullable)dataSetForLabel:(NSString * _Nonnull)label ignorecase:(BOOL)ignorecase SWIFT_WARN_UNUSED_RESULT;
- (ChartDataEntry * _Nullable)entryFor:(ChartHighlight * _Nonnull)highlight SWIFT_WARN_UNUSED_RESULT;
/// The total y-value sum across all DataSet objects the this object represents.
@property (nonatomic, readonly) double yValueSum;
@end

SWIFT_CLASS("_TtC8DGCharts17PieChartDataEntry")
@interface PieChartDataEntry : ChartDataEntry
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// \param value The value on the y-axis
///
- (nonnull instancetype)initWithValue:(double)value OBJC_DESIGNATED_INITIALIZER;
/// \param value The value on the y-axis
///
/// \param label The label for the x-axis
///
- (nonnull instancetype)initWithValue:(double)value label:(NSString * _Nullable)label;
/// \param value The value on the y-axis
///
/// \param label The label for the x-axis
///
/// \param data Spot for additional data this Entry represents
///
- (nonnull instancetype)initWithValue:(double)value label:(NSString * _Nullable)label data:(id _Nullable)data;
/// \param value The value on the y-axis
///
/// \param label The label for the x-axis
///
/// \param icon icon image
///
- (nonnull instancetype)initWithValue:(double)value label:(NSString * _Nullable)label icon:(UIImage * _Nullable)icon;
/// \param value The value on the y-axis
///
/// \param label The label for the x-axis
///
/// \param icon icon image
///
/// \param data Spot for additional data this Entry represents
///
- (nonnull instancetype)initWithValue:(double)value label:(NSString * _Nullable)label icon:(UIImage * _Nullable)icon data:(id _Nullable)data;
/// \param value The value on the y-axis
///
/// \param data Spot for additional data this Entry represents
///
- (nonnull instancetype)initWithValue:(double)value data:(id _Nullable)data;
/// \param value The value on the y-axis
///
/// \param icon icon image
///
- (nonnull instancetype)initWithValue:(double)value icon:(UIImage * _Nullable)icon;
/// \param value The value on the y-axis
///
/// \param icon icon image
///
/// \param data Spot for additional data this Entry represents
///
- (nonnull instancetype)initWithValue:(double)value icon:(UIImage * _Nullable)icon data:(id _Nullable)data;
@property (nonatomic, copy) NSString * _Nullable label;
@property (nonatomic) double value;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithX:(double)x y:(double)y SWIFT_UNAVAILABLE;
@end

enum PieChartValuePosition : NSInteger;
SWIFT_PROTOCOL("_TtP8DGCharts23PieChartDataSetProtocol_")
@protocol PieChartDataSetProtocol <ChartDataSetProtocol>
/// the space in pixels between the pie-slices
/// <em>default</em>: 0
/// <em>maximum</em>: 20
@property (nonatomic) CGFloat sliceSpace;
/// When enabled, slice spacing will be 0.0 when the smallest value is going to be smaller than the slice spacing itself.
@property (nonatomic) BOOL automaticallyDisableSliceSpacing;
/// indicates the selection distance of a pie slice
@property (nonatomic) CGFloat selectionShift;
@property (nonatomic) enum PieChartValuePosition xValuePosition;
@property (nonatomic) enum PieChartValuePosition yValuePosition;
/// When valuePosition is OutsideSlice, indicates line color
@property (nonatomic, strong) UIColor * _Nullable valueLineColor;
/// When valuePosition is OutsideSlice and enabled, line will have the same color as the slice
@property (nonatomic) BOOL useValueColorForLine;
/// When valuePosition is OutsideSlice, indicates line width
@property (nonatomic) CGFloat valueLineWidth;
/// When valuePosition is OutsideSlice, indicates offset as percentage out of the slice size
@property (nonatomic) CGFloat valueLinePart1OffsetPercentage;
/// When valuePosition is OutsideSlice, indicates length of first half of the line
@property (nonatomic) CGFloat valueLinePart1Length;
/// When valuePosition is OutsideSlice, indicates length of second half of the line
@property (nonatomic) CGFloat valueLinePart2Length;
/// When valuePosition is OutsideSlice, this allows variable line length
@property (nonatomic) BOOL valueLineVariableLength;
/// the font for the slice-text labels
@property (nonatomic, strong) UIFont * _Nullable entryLabelFont;
/// the color for the slice-text labels
@property (nonatomic, strong) UIColor * _Nullable entryLabelColor;
/// get/sets the color for the highlighted sector
@property (nonatomic, strong) UIColor * _Nullable highlightColor;
@end

SWIFT_CLASS("_TtC8DGCharts15PieChartDataSet")
@interface PieChartDataSet : ChartDataSet <PieChartDataSetProtocol>
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
/// the space in pixels between the pie-slices
/// <em>default</em>: 0
/// <em>maximum</em>: 20
@property (nonatomic) CGFloat sliceSpace;
/// When enabled, slice spacing will be 0.0 when the smallest value is going to be smaller than the slice spacing itself.
@property (nonatomic) BOOL automaticallyDisableSliceSpacing;
/// indicates the selection distance of a pie slice
@property (nonatomic) CGFloat selectionShift;
@property (nonatomic) enum PieChartValuePosition xValuePosition;
@property (nonatomic) enum PieChartValuePosition yValuePosition;
/// When valuePosition is OutsideSlice, indicates line color
@property (nonatomic, strong) UIColor * _Nullable valueLineColor;
/// When valuePosition is OutsideSlice and enabled, line will have the same color as the slice
@property (nonatomic) BOOL useValueColorForLine;
/// When valuePosition is OutsideSlice, indicates line width
@property (nonatomic) CGFloat valueLineWidth;
/// When valuePosition is OutsideSlice, indicates offset as percentage out of the slice size
@property (nonatomic) CGFloat valueLinePart1OffsetPercentage;
/// When valuePosition is OutsideSlice, indicates length of first half of the line
@property (nonatomic) CGFloat valueLinePart1Length;
/// When valuePosition is OutsideSlice, indicates length of second half of the line
@property (nonatomic) CGFloat valueLinePart2Length;
/// When valuePosition is OutsideSlice, this allows variable line length
@property (nonatomic) BOOL valueLineVariableLength;
/// the font for the slice-text labels
@property (nonatomic, strong) UIFont * _Nullable entryLabelFont;
/// the color for the slice-text labels
@property (nonatomic, strong) UIColor * _Nullable entryLabelColor;
/// the color for the highlighted sector
@property (nonatomic, strong) UIColor * _Nullable highlightColor;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
@end

typedef SWIFT_ENUM_NAMED(NSInteger, PieChartValuePosition, "ValuePosition", closed) {
  PieChartValuePositionInsideSlice = 0,
  PieChartValuePositionOutsideSlice = 1,
};

@class PieChartView;
SWIFT_CLASS("_TtC8DGCharts16PieChartRenderer")
@interface PieChartRenderer : NSObject <ChartDataRenderer>
@property (nonatomic, readonly, strong) ChartViewPortHandler * _Nonnull viewPortHandler;
@property (nonatomic, copy) NSArray<NSUIAccessibilityElement *> * _Nonnull accessibleChartElements;
@property (nonatomic, readonly, strong) ChartAnimator * _Nonnull animator;
@property (nonatomic, weak) PieChartView * _Nullable chart;
- (nonnull instancetype)initWithChart:(PieChartView * _Nonnull)chart animator:(ChartAnimator * _Nonnull)animator viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
- (void)drawDataWithContext:(CGContextRef _Nonnull)context;
- (CGFloat)calculateMinimumRadiusForSpacedSliceWithCenter:(CGPoint)center radius:(CGFloat)radius angle:(CGFloat)angle arcStartPointX:(CGFloat)arcStartPointX arcStartPointY:(CGFloat)arcStartPointY startAngle:(CGFloat)startAngle sweepAngle:(CGFloat)sweepAngle SWIFT_WARN_UNUSED_RESULT;
/// Calculates the sliceSpace to use based on visible values and their size compared to the set sliceSpace.
- (CGFloat)getSliceSpaceWithDataSet:(id <PieChartDataSetProtocol> _Nonnull)dataSet SWIFT_WARN_UNUSED_RESULT;
- (void)drawDataSetWithContext:(CGContextRef _Nonnull)context dataSet:(id <PieChartDataSetProtocol> _Nonnull)dataSet;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
- (void)drawExtrasWithContext:(CGContextRef _Nonnull)context;
- (void)initBuffers SWIFT_METHOD_FAMILY(none);
- (BOOL)isDrawingValuesAllowedWithDataProvider:(id <ChartDataProvider> _Nullable)dataProvider SWIFT_WARN_UNUSED_RESULT;
- (void)drawHighlightedWithContext:(CGContextRef _Nonnull)context indices:(NSArray<ChartHighlight *> * _Nonnull)highlights;
- (NSUIAccessibilityElement * _Nonnull)createAccessibleHeaderUsingChart:(ChartViewBase * _Nonnull)chart andData:(ChartData * _Nonnull)data withDefaultDescription:(NSString * _Nonnull)defaultDescription SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Base class of PieChartView and RadarChartView.
SWIFT_CLASS("_TtC8DGCharts21PieRadarChartViewBase")
@interface PieRadarChartViewBase : ChartViewBase
/// flag that indicates if rotation is enabled or not
@property (nonatomic) BOOL rotationEnabled;
/// Sets the minimum offset (padding) around the chart, defaults to 0.0
@property (nonatomic) CGFloat minOffset;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, readonly) NSInteger maxVisibleCount;
- (void)notifyDataSetChanged;
///
/// returns:
/// The angle relative to the chart center for the given point on the chart in degrees.
/// The angle is always between 0 and 360°, 0° is NORTH, 90° is EAST, …
- (CGFloat)angleForPointWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// Calculates the position around a center point, depending on the distance
/// from the center, and the angle of the position around the center.
- (CGPoint)getPositionWithCenter:(CGPoint)center dist:(CGFloat)dist angle:(CGFloat)angle SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The distance of a certain point on the chart to the center of the chart.
- (CGFloat)distanceToCenterWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The xIndex for the given angle around the center of the chart.
/// -1 if not found / outofbounds.
- (NSInteger)indexForAngle:(CGFloat)angle SWIFT_WARN_UNUSED_RESULT;
/// current rotation angle of the pie chart
/// <em>default</em>: 270 –> top (NORTH)
/// Will always return a normalized value, which will be between 0.0 < 360.0
@property (nonatomic) CGFloat rotationAngle;
/// gets the raw version of the current rotation angle of the pie chart the returned value could be any value, negative or positive, outside of the 360 degrees.
/// this is used when working with rotation direction, mainly by gestures and animations.
@property (nonatomic, readonly) CGFloat rawRotationAngle;
/// The diameter of the pie- or radar-chart
@property (nonatomic, readonly) CGFloat diameter;
/// The radius of the chart in pixels.
@property (nonatomic, readonly) CGFloat radius;
@property (nonatomic, readonly) double chartYMax;
@property (nonatomic, readonly) double chartYMin;
@property (nonatomic, readonly) BOOL isRotationEnabled;
/// flag that indicates if rotation is done with two fingers or one.
/// when the chart is inside a scrollview, you need a two-finger rotation because a one-finger rotation eats up all touch events.
/// On iOS this will disable one-finger rotation.
/// On OSX this will keep two-finger multitouch rotation, and one-pointer mouse rotation.
/// <em>default</em>: false
@property (nonatomic) BOOL rotationWithTwoFingers;
/// flag that indicates if rotation is done with two fingers or one.
/// when the chart is inside a scrollview, you need a two-finger rotation because a one-finger rotation eats up all touch events.
/// On iOS this will disable one-finger rotation.
/// On OSX this will keep two-finger multitouch rotation, and one-pointer mouse rotation.
/// <em>default</em>: false
@property (nonatomic, readonly) BOOL isRotationWithTwoFingers;
/// Applys a spin animation to the Chart.
- (void)spinWithDuration:(NSTimeInterval)duration fromAngle:(CGFloat)fromAngle toAngle:(CGFloat)toAngle easing:(double (^ _Nullable)(NSTimeInterval, NSTimeInterval))easing;
- (void)spinWithDuration:(NSTimeInterval)duration fromAngle:(CGFloat)fromAngle toAngle:(CGFloat)toAngle easingOption:(enum ChartEasingOption)easingOption;
- (void)spinWithDuration:(NSTimeInterval)duration fromAngle:(CGFloat)fromAngle toAngle:(CGFloat)toAngle;
- (void)stopSpinAnimation;
- (void)nsuiTouchesBegan:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)nsuiTouchesMoved:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)nsuiTouchesEnded:(NSSet<UITouch *> * _Nonnull)touches withEvent:(UIEvent * _Nullable)event;
- (void)nsuiTouchesCancelled:(NSSet<UITouch *> * _Nullable)touches withEvent:(UIEvent * _Nullable)event;
- (void)stopDeceleration;
@end

@class NSAttributedString;
/// View that represents a pie chart. Draws cake like slices.
SWIFT_CLASS("_TtC8DGCharts12PieChartView")
@interface PieChartView : PieRadarChartViewBase
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
- (void)drawRect:(CGRect)rect;
- (CGFloat)angleForPointWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The distance of a certain point on the chart to the center of the chart.
- (CGFloat)distanceToCenterWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
- (CGPoint)getMarkerPositionWithHighlight:(ChartHighlight * _Nonnull)highlight SWIFT_WARN_UNUSED_RESULT;
/// Checks if the given index is set to be highlighted.
- (BOOL)needsHighlightWithIndex:(NSInteger)index SWIFT_WARN_UNUSED_RESULT;
/// This will throw an exception, PieChart has no XAxis object.
@property (nonatomic, strong) ChartXAxis * _Nonnull xAxis;
- (NSInteger)indexForAngle:(CGFloat)angle SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The index of the DataSet this x-index belongs to.
- (NSInteger)dataSetIndexForIndex:(double)xValue SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// An integer array of all the different angles the chart slices
/// have the angles in the returned array determine how much space (of 360°)
/// each slice takes
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull drawAngles;
///
/// returns:
/// The absolute angles of the different chart slices (where the
/// slices end)
@property (nonatomic, readonly, copy) NSArray<NSNumber *> * _Nonnull absoluteAngles;
/// The color for the hole that is drawn in the center of the PieChart (if enabled).
/// note:
/// Use holeTransparent with holeColor = nil to make the hole transparent.*
@property (nonatomic, strong) UIColor * _Nullable holeColor;
/// if true, the hole will see-through to the inner tips of the slices
/// <em>default</em>: <code>false</code>
@property (nonatomic) BOOL drawSlicesUnderHoleEnabled;
/// <code>true</code> if the inner tips of the slices are visible behind the hole, <code>false</code> if not.
@property (nonatomic, readonly) BOOL isDrawSlicesUnderHoleEnabled;
/// <code>true</code> if the hole in the center of the pie-chart is set to be visible, <code>false</code> ifnot
@property (nonatomic) BOOL drawHoleEnabled;
/// <code>true</code> if the hole in the center of the pie-chart is set to be visible, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawHoleEnabled;
/// the text that is displayed in the center of the pie-chart
@property (nonatomic, copy) NSString * _Nullable centerText;
/// the text that is displayed in the center of the pie-chart
@property (nonatomic, strong) NSAttributedString * _Nullable centerAttributedText;
/// Sets the offset the center text should have from it’s original position in dp. Default x = 0, y = 0
@property (nonatomic) CGPoint centerTextOffset;
/// <code>true</code> if drawing the center text is enabled
@property (nonatomic) BOOL drawCenterTextEnabled;
/// <code>true</code> if drawing the center text is enabled
@property (nonatomic, readonly) BOOL isDrawCenterTextEnabled;
@property (nonatomic, readonly) CGFloat radius;
/// The circlebox, the boundingbox of the pie-chart slices
@property (nonatomic, readonly) CGRect circleBox;
/// The center of the circlebox
@property (nonatomic, readonly) CGPoint centerCircleBox;
/// the radius of the hole in the center of the piechart in percent of the maximum radius (max = the radius of the whole chart)
/// <em>default</em>: 0.5 (50%) (half the pie)
@property (nonatomic) CGFloat holeRadiusPercent;
/// The color that the transparent-circle should have.
/// <em>default</em>: <code>nil</code>
@property (nonatomic, strong) UIColor * _Nullable transparentCircleColor;
/// the radius of the transparent circle that is drawn next to the hole in the piechart in percent of the maximum radius (max = the radius of the whole chart)
/// <em>default</em>: 0.55 (55%) -> means 5% larger than the center-hole by default
@property (nonatomic) CGFloat transparentCircleRadiusPercent;
/// The color the entry labels are drawn with.
@property (nonatomic, strong) UIColor * _Nullable entryLabelColor;
/// The font the entry labels are drawn with.
@property (nonatomic, strong) UIFont * _Nullable entryLabelFont;
/// Set this to true to draw the enrty labels into the pie slices
@property (nonatomic) BOOL drawEntryLabelsEnabled;
/// <code>true</code> if drawing entry labels is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawEntryLabelsEnabled;
/// If this is enabled, values inside the PieChart are drawn in percent and not with their original value. Values provided for the ValueFormatter to format are then provided in percent.
@property (nonatomic) BOOL usePercentValuesEnabled;
/// <code>true</code> if drawing x-values is enabled, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isUsePercentValuesEnabled;
/// the rectangular radius of the bounding box for the center text, as a percentage of the pie hole
@property (nonatomic) CGFloat centerTextRadiusPercent;
/// The max angle that is used for calculating the pie-circle.
/// 360 means it’s a full pie-chart, 180 results in a half-pie-chart.
/// <em>default</em>: 360.0
@property (nonatomic) CGFloat maxAngle;
/// smallest pie slice angle that will have a label drawn in degrees, 0 by default
@property (nonatomic) CGFloat sliceTextDrawingThreshold;
@end

SWIFT_CLASS_NAMED("PieRadarHighlighter")
@interface PieRadarChartHighlighter : ChartHighlighter
- (ChartHighlight * _Nullable)getHighlightWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// \param index 
///
/// \param x 
///
/// \param y 
///
///
/// returns:
/// The closest Highlight object of the given objects based on the touch position inside the chart.
- (ChartHighlight * _Nullable)closestHighlightWithIndex:(NSInteger)index x:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithChart:(id <ChartDataProvider> _Nonnull)chart OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS_NAMED("PieHighlighter")
@interface PieChartHighlighter : PieRadarChartHighlighter
- (ChartHighlight * _Nullable)closestHighlightWithIndex:(NSInteger)index x:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithChart:(id <ChartDataProvider> _Nonnull)chart OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts14RadarChartData")
@interface RadarChartData : ChartData
@property (nonatomic, strong) UIColor * _Nonnull highlightColor;
@property (nonatomic) CGFloat highlightLineWidth;
@property (nonatomic) CGFloat highlightLineDashPhase;
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable highlightLineDashLengths;
/// Sets labels that should be drawn around the RadarChart at the end of each web line.
@property (nonatomic, copy) NSArray<NSString *> * _Nonnull labels;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets OBJC_DESIGNATED_INITIALIZER;
- (ChartDataEntry * _Nullable)entryFor:(ChartHighlight * _Nonnull)highlight SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_CLASS("_TtC8DGCharts19RadarChartDataEntry")
@interface RadarChartDataEntry : ChartDataEntry
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
/// \param value The value on the y-axis.
///
- (nonnull instancetype)initWithValue:(double)value OBJC_DESIGNATED_INITIALIZER;
/// \param value The value on the y-axis.
///
/// \param data Spot for additional data this Entry represents.
///
- (nonnull instancetype)initWithValue:(double)value data:(id _Nullable)data;
@property (nonatomic) double value;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithX:(double)x y:(double)y SWIFT_UNAVAILABLE;
@end

SWIFT_PROTOCOL("_TtP8DGCharts25RadarChartDataSetProtocol_")
@protocol RadarChartDataSetProtocol <LineRadarChartDataSetProtocol>
/// flag indicating whether highlight circle should be drawn or not
@property (nonatomic) BOOL drawHighlightCircleEnabled;
@property (nonatomic, readonly) BOOL isDrawHighlightCircleEnabled;
@property (nonatomic, strong) UIColor * _Nullable highlightCircleFillColor;
/// The stroke color for highlight circle.
/// If <code>nil</code>, the color of the dataset is taken.
@property (nonatomic, strong) UIColor * _Nullable highlightCircleStrokeColor;
@property (nonatomic) CGFloat highlightCircleStrokeAlpha;
@property (nonatomic) CGFloat highlightCircleInnerRadius;
@property (nonatomic) CGFloat highlightCircleOuterRadius;
@property (nonatomic) CGFloat highlightCircleStrokeWidth;
@end

SWIFT_CLASS("_TtC8DGCharts17RadarChartDataSet")
@interface RadarChartDataSet : LineRadarChartDataSet <RadarChartDataSetProtocol>
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
/// flag indicating whether highlight circle should be drawn or not
/// <em>default</em>: false
@property (nonatomic) BOOL drawHighlightCircleEnabled;
/// <code>true</code> if highlight circle should be drawn, <code>false</code> ifnot
@property (nonatomic, readonly) BOOL isDrawHighlightCircleEnabled;
@property (nonatomic, strong) UIColor * _Nullable highlightCircleFillColor;
/// The stroke color for highlight circle.
/// If <code>nil</code>, the color of the dataset is taken.
@property (nonatomic, strong) UIColor * _Nullable highlightCircleStrokeColor;
@property (nonatomic) CGFloat highlightCircleStrokeAlpha;
@property (nonatomic) CGFloat highlightCircleInnerRadius;
@property (nonatomic) CGFloat highlightCircleOuterRadius;
@property (nonatomic) CGFloat highlightCircleStrokeWidth;
@end

@class RadarChartView;
SWIFT_CLASS("_TtC8DGCharts18RadarChartRenderer")
@interface RadarChartRenderer : LineRadarChartRenderer
@property (nonatomic, weak) RadarChartView * _Nullable chart;
- (nonnull instancetype)initWithChart:(RadarChartView * _Nonnull)chart animator:(ChartAnimator * _Nonnull)animator viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
- (void)drawDataWithContext:(CGContextRef _Nonnull)context;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
- (void)drawExtrasWithContext:(CGContextRef _Nonnull)context;
- (void)drawWebWithContext:(CGContextRef _Nonnull)context;
- (void)drawHighlightedWithContext:(CGContextRef _Nonnull)context indices:(NSArray<ChartHighlight *> * _Nonnull)indices;
@end

/// Implementation of the RadarChart, a “spidernet”-like chart. It works best
/// when displaying 5-10 entries per DataSet.
SWIFT_CLASS("_TtC8DGCharts14RadarChartView")
@interface RadarChartView : PieRadarChartViewBase
/// width of the web lines that come from the center.
@property (nonatomic) CGFloat webLineWidth;
/// width of the web lines that are in between the lines coming from the center
@property (nonatomic) CGFloat innerWebLineWidth;
/// color for the web lines that come from the center
@property (nonatomic, strong) UIColor * _Nonnull webColor;
/// color for the web lines in between the lines that come from the center.
@property (nonatomic, strong) UIColor * _Nonnull innerWebColor;
/// transparency the grid is drawn with (0.0 - 1.0)
@property (nonatomic) CGFloat webAlpha;
/// flag indicating if the web lines should be drawn or not
@property (nonatomic) BOOL drawWeb;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
- (void)notifyDataSetChanged;
- (void)drawRect:(CGRect)rect;
/// The factor that is needed to transform values into pixels.
@property (nonatomic, readonly) CGFloat factor;
/// The angle that each slice in the radar chart occupies.
@property (nonatomic, readonly) CGFloat sliceAngle;
- (NSInteger)indexForAngle:(CGFloat)angle SWIFT_WARN_UNUSED_RESULT;
/// The object that represents all y-labels of the RadarChart.
@property (nonatomic, readonly, strong) ChartYAxis * _Nonnull yAxis;
/// Sets the number of web-lines that should be skipped on chart web before the next one is drawn. This targets the lines that come from the center of the RadarChart.
/// if count = 1 -> 1 line is skipped in between
@property (nonatomic) NSInteger skipWebLineCount;
@property (nonatomic, readonly) CGFloat radius;
/// The maximum value this chart can display on it’s y-axis.
@property (nonatomic, readonly) double chartYMax;
/// The minimum value this chart can display on it’s y-axis.
@property (nonatomic, readonly) double chartYMin;
/// The range of y-values this chart can display.
@property (nonatomic, readonly) double yRange;
@end

SWIFT_CLASS_NAMED("RadarHighlighter")
@interface RadarChartHighlighter : PieRadarChartHighlighter
- (ChartHighlight * _Nullable)closestHighlightWithIndex:(NSInteger)index x:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithChart:(id <ChartDataProvider> _Nonnull)chart OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS_NAMED("RadialGradientFill")
@interface ChartRadialGradientFill : NSObject <ChartFill>
@property (nonatomic, readonly) CGGradientRef _Nonnull gradient;
@property (nonatomic, readonly) CGPoint startOffsetPercent;
@property (nonatomic, readonly) CGPoint endOffsetPercent;
@property (nonatomic, readonly) CGFloat startRadiusPercent;
@property (nonatomic, readonly) CGFloat endRadiusPercent;
- (nonnull instancetype)initWithGradient:(CGGradientRef _Nonnull)gradient startOffsetPercent:(CGPoint)startOffsetPercent endOffsetPercent:(CGPoint)endOffsetPercent startRadiusPercent:(CGFloat)startRadiusPercent endRadiusPercent:(CGFloat)endRadiusPercent OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithGradient:(CGGradientRef _Nonnull)gradient;
- (void)fillPathWithContext:(CGContextRef _Nonnull)context rect:(CGRect)rect;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS_NAMED("Range")
@interface ChartRange : NSObject
@property (nonatomic) double from;
@property (nonatomic) double to;
- (nonnull instancetype)initFrom:(double)from to:(double)to OBJC_DESIGNATED_INITIALIZER;
/// \param value 
///
///
/// returns:
/// <code>true</code> if this range contains (if the value is in between) the given value, <code>false</code> ifnot.
- (BOOL)contains:(double)value SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isLarger:(double)value SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isSmaller:(double)value SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS("_TtC8DGCharts16ScatterChartData")
@interface ScatterChartData : BarLineScatterCandleBubbleChartData
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithDataSets:(NSArray<id <ChartDataSetProtocol>> * _Nonnull)dataSets OBJC_DESIGNATED_INITIALIZER;
///
/// returns:
/// The maximum shape-size across all DataSets.
- (CGFloat)getGreatestShapeSize SWIFT_WARN_UNUSED_RESULT;
@end

SWIFT_PROTOCOL("_TtP8DGCharts27ScatterChartDataSetProtocol_")
@protocol ScatterChartDataSetProtocol <LineScatterCandleRadarChartDataSetProtocol>
///
/// returns:
/// The size the scatter shape will have
@property (nonatomic, readonly) CGFloat scatterShapeSize;
///
/// returns:
/// The radius of the hole in the shape (applies to Square, Circle and Triangle)
/// Set this to <= 0 to remove holes.
/// <em>default</em>: 0.0
@property (nonatomic, readonly) CGFloat scatterShapeHoleRadius;
///
/// returns:
/// Color for the hole in the shape. Setting to <code>nil</code> will behave as transparent.
/// <em>default</em>: nil
@property (nonatomic, readonly, strong) UIColor * _Nullable scatterShapeHoleColor;
///
/// returns:
/// The ShapeRenderer responsible for rendering this DataSet.
@property (nonatomic, readonly, strong) id <ShapeRenderer> _Nullable shapeRenderer;
@end

enum ScatterShape : NSInteger;
SWIFT_CLASS("_TtC8DGCharts19ScatterChartDataSet")
@interface ScatterChartDataSet : LineScatterCandleRadarChartDataSet <ScatterChartDataSetProtocol>
/// The size the scatter shape will have
@property (nonatomic) CGFloat scatterShapeSize;
/// The radius of the hole in the shape (applies to Square, Circle and Triangle)
/// <em>default</em>: 0.0
@property (nonatomic) CGFloat scatterShapeHoleRadius;
/// Color for the hole in the shape. Setting to <code>nil</code> will behave as transparent.
/// <em>default</em>: nil
@property (nonatomic, strong) UIColor * _Nullable scatterShapeHoleColor;
/// Sets the ScatterShape this DataSet should be drawn with.
/// This will search for an available ShapeRenderer and set this renderer for the DataSet
- (void)setScatterShape:(enum ScatterShape)shape;
/// The IShapeRenderer responsible for rendering this DataSet.
/// This can also be used to set a custom IShapeRenderer aside from the default ones.
/// <em>default</em>: <code>SquareShapeRenderer</code>
@property (nonatomic, strong) id <ShapeRenderer> _Nullable shapeRenderer;
+ (id <ShapeRenderer> _Nonnull)rendererForShape:(enum ScatterShape)shape SWIFT_WARN_UNUSED_RESULT;
- (id _Nonnull)copyWithZone:(struct _NSZone * _Nullable)zone SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithEntries:(NSArray<ChartDataEntry *> * _Nonnull)entries label:(NSString * _Nonnull)label OBJC_DESIGNATED_INITIALIZER;
@end

typedef SWIFT_ENUM_NAMED(NSInteger, ScatterShape, "Shape", closed) {
  ScatterShapeSquare = 0,
  ScatterShapeCircle = 1,
  ScatterShapeTriangle = 2,
  ScatterShapeCross = 3,
  ScatterShapeX = 4,
  ScatterShapeChevronUp = 5,
  ScatterShapeChevronDown = 6,
};

SWIFT_CLASS("_TtC8DGCharts20ScatterChartRenderer")
@interface ScatterChartRenderer : LineScatterCandleRadarChartRenderer
@property (nonatomic, weak) id <ScatterChartDataProvider> _Nullable dataProvider;
- (nonnull instancetype)initWithDataProvider:(id <ScatterChartDataProvider> _Nonnull)dataProvider animator:(ChartAnimator * _Nonnull)animator viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
- (void)drawDataWithContext:(CGContextRef _Nonnull)context;
- (void)drawDataSetWithContext:(CGContextRef _Nonnull)context dataSet:(id <ScatterChartDataSetProtocol> _Nonnull)dataSet;
- (void)drawValuesWithContext:(CGContextRef _Nonnull)context;
- (void)drawExtrasWithContext:(CGContextRef _Nonnull)context;
- (void)drawHighlightedWithContext:(CGContextRef _Nonnull)context indices:(NSArray<ChartHighlight *> * _Nonnull)indices;
@end

/// The ScatterChart. Draws dots, triangles, squares and custom shapes into the chartview.
SWIFT_CLASS("_TtC8DGCharts16ScatterChartView")
@interface ScatterChartView : BarLineChartViewBase <ScatterChartDataProvider>
@property (nonatomic, readonly, strong) ScatterChartData * _Nullable scatterData;
- (nonnull instancetype)initWithFrame:(CGRect)frame OBJC_DESIGNATED_INITIALIZER;
- (nullable instancetype)initWithCoder:(NSCoder * _Nonnull)aDecoder OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts19SquareShapeRenderer")
@interface SquareShapeRenderer : NSObject <ShapeRenderer>
- (void)renderShapeWithContext:(CGContextRef _Nonnull)context dataSet:(id <ScatterChartDataSetProtocol> _Nonnull)dataSet viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler point:(CGPoint)point color:(UIColor * _Nonnull)color;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// Transformer class that contains all matrices and is responsible for transforming values into pixels on the screen and backwards.
SWIFT_CLASS_NAMED("Transformer")
@interface ChartTransformer : NSObject
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
/// Prepares the matrix that transforms values to pixels. Calculates the scale factors from the charts size and offsets.
- (void)prepareMatrixValuePxWithChartXMin:(double)chartXMin deltaX:(CGFloat)deltaX deltaY:(CGFloat)deltaY chartYMin:(double)chartYMin;
/// Prepares the matrix that contains all offsets.
- (void)prepareMatrixOffsetWithInverted:(BOOL)inverted;
- (CGPoint)pixelForValuesWithX:(double)x y:(double)y SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The x and y values in the chart at the given touch point
/// (encapsulated in a CGPoint). This method transforms pixel coordinates to
/// coordinates / values in the chart.
- (CGPoint)valueForTouchPoint:(CGPoint)point SWIFT_WARN_UNUSED_RESULT;
///
/// returns:
/// The x and y values in the chart at the given touch point
/// (x/y). This method transforms pixel coordinates to
/// coordinates / values in the chart.
- (CGPoint)valueForTouchPointWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, readonly) CGAffineTransform valueToPixelMatrix;
@property (nonatomic, readonly) CGAffineTransform pixelToValueMatrix;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS_NAMED("TransformerHorizontalBarChart")
@interface ChartTransformerHorizontalBarChart : ChartTransformer
/// Prepares the matrix that contains all offsets.
- (void)prepareMatrixOffsetWithInverted:(BOOL)inverted;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS("_TtC8DGCharts21TriangleShapeRenderer")
@interface TriangleShapeRenderer : NSObject <ShapeRenderer>
- (void)renderShapeWithContext:(CGContextRef _Nonnull)context dataSet:(id <ScatterChartDataSetProtocol> _Nonnull)dataSet viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler point:(CGPoint)point color:(UIColor * _Nonnull)color;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// Class that contains information about the charts current viewport settings, including offsets, scale & translation levels, …
SWIFT_CLASS_NAMED("ViewPortHandler")
@interface ChartViewPortHandler : NSObject
/// matrix used for touch events
@property (nonatomic, readonly) CGAffineTransform touchMatrix;
/// this rectangle defines the area in which graph values can be drawn
@property (nonatomic, readonly) CGRect contentRect;
@property (nonatomic, readonly) CGFloat chartWidth;
@property (nonatomic, readonly) CGFloat chartHeight;
/// minimum scale value on the y-axis
@property (nonatomic, readonly) CGFloat minScaleY;
/// maximum scale value on the y-axis
@property (nonatomic, readonly) CGFloat maxScaleY;
/// minimum scale value on the x-axis
@property (nonatomic, readonly) CGFloat minScaleX;
/// maximum scale value on the x-axis
@property (nonatomic, readonly) CGFloat maxScaleX;
/// contains the current scale factor of the x-axis
@property (nonatomic, readonly) CGFloat scaleX;
/// contains the current scale factor of the y-axis
@property (nonatomic, readonly) CGFloat scaleY;
/// current translation (drag / pan) distance on the x-axis
@property (nonatomic, readonly) CGFloat transX;
/// current translation (drag / pan) distance on the y-axis
@property (nonatomic, readonly) CGFloat transY;
/// Constructor - don’t forget calling setChartDimens(…)
- (nonnull instancetype)initWithWidth:(CGFloat)width height:(CGFloat)height OBJC_DESIGNATED_INITIALIZER;
- (void)setChartDimensWithWidth:(CGFloat)width height:(CGFloat)height;
@property (nonatomic, readonly) BOOL hasChartDimens;
- (void)restrainViewPortWithOffsetLeft:(CGFloat)offsetLeft offsetTop:(CGFloat)offsetTop offsetRight:(CGFloat)offsetRight offsetBottom:(CGFloat)offsetBottom;
@property (nonatomic, readonly) CGFloat offsetLeft;
@property (nonatomic, readonly) CGFloat offsetRight;
@property (nonatomic, readonly) CGFloat offsetTop;
@property (nonatomic, readonly) CGFloat offsetBottom;
@property (nonatomic, readonly) CGFloat contentTop;
@property (nonatomic, readonly) CGFloat contentLeft;
@property (nonatomic, readonly) CGFloat contentRight;
@property (nonatomic, readonly) CGFloat contentBottom;
@property (nonatomic, readonly) CGFloat contentWidth;
@property (nonatomic, readonly) CGFloat contentHeight;
@property (nonatomic, readonly) CGPoint contentCenter;
/// Zooms by the specified zoom factors.
- (CGAffineTransform)zoomWithScaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY SWIFT_WARN_UNUSED_RESULT;
/// Zooms around the specified center
- (CGAffineTransform)zoomWithScaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY x:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// Zooms in by 1.4, x and y are the coordinates (in pixels) of the zoom center.
- (CGAffineTransform)zoomInX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// Zooms out by 0.7, x and y are the coordinates (in pixels) of the zoom center.
- (CGAffineTransform)zoomOutWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// Zooms out to original size.
- (CGAffineTransform)resetZoom SWIFT_WARN_UNUSED_RESULT;
/// Sets the scale factor to the specified values.
- (CGAffineTransform)setZoomWithScaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY SWIFT_WARN_UNUSED_RESULT;
/// Sets the scale factor to the specified values. x and y is pivot.
- (CGAffineTransform)setZoomWithScaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY x:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// Resets all zooming and dragging and makes the chart fit exactly it’s bounds.
- (CGAffineTransform)fitScreen SWIFT_WARN_UNUSED_RESULT;
/// Translates to the specified point.
- (CGAffineTransform)translateWithPt:(CGPoint)pt SWIFT_WARN_UNUSED_RESULT;
/// Centers the viewport around the specified position (x-index and y-value) in the chart.
/// Centering the viewport outside the bounds of the chart is not possible.
/// Makes most sense in combination with the setScaleMinima(…) method.
- (void)centerViewPortWithPt:(CGPoint)pt chart:(ChartViewBase * _Nonnull)chart;
/// call this method to refresh the graph with a given matrix
- (CGAffineTransform)refreshWithNewMatrix:(CGAffineTransform)newMatrix chart:(ChartViewBase * _Nonnull)chart invalidate:(BOOL)invalidate;
/// Sets the minimum scale factor for the x-axis
- (void)setMinimumScaleX:(CGFloat)xScale;
/// Sets the maximum scale factor for the x-axis
- (void)setMaximumScaleX:(CGFloat)xScale;
/// Sets the minimum and maximum scale factors for the x-axis
- (void)setMinMaxScaleXWithMinScaleX:(CGFloat)minScaleX maxScaleX:(CGFloat)maxScaleX;
/// Sets the minimum scale factor for the y-axis
- (void)setMinimumScaleY:(CGFloat)yScale;
/// Sets the maximum scale factor for the y-axis
- (void)setMaximumScaleY:(CGFloat)yScale;
- (void)setMinMaxScaleYWithMinScaleY:(CGFloat)minScaleY maxScaleY:(CGFloat)maxScaleY;
- (BOOL)isInBoundsX:(CGFloat)x SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isInBoundsY:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// A method to check whether coordinate lies within the viewport.
/// \param point a coordinate.
///
- (BOOL)isInBoundsWithPoint:(CGPoint)point SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isInBoundsWithX:(CGFloat)x y:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isInBoundsLeft:(CGFloat)x SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isInBoundsRight:(CGFloat)x SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isInBoundsTop:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isInBoundsBottom:(CGFloat)y SWIFT_WARN_UNUSED_RESULT;
/// A method to check whether a line between two coordinates intersects with the view port  by using a linear function.
/// Linear function (calculus): <code>y = ax + b</code>
/// Note: this method will not check for collision with the right edge of the view port, as we assume lines run from left
/// to right (e.g. <code>startPoint < endPoint</code>).
/// \param startPoint the start coordinate of the line.
///
/// \param endPoint the end coordinate of the line.
///
- (BOOL)isIntersectingLineFrom:(CGPoint)startPoint to:(CGPoint)endPoint SWIFT_WARN_UNUSED_RESULT;
/// if the chart is fully zoomed out, return true
@property (nonatomic, readonly) BOOL isFullyZoomedOut;
/// <code>true</code> if the chart is fully zoomed out on it’s y-axis (vertical).
@property (nonatomic, readonly) BOOL isFullyZoomedOutY;
/// <code>true</code> if the chart is fully zoomed out on it’s x-axis (horizontal).
@property (nonatomic, readonly) BOOL isFullyZoomedOutX;
/// Set an offset in pixels that allows the user to drag the chart over it’s bounds on the x-axis.
- (void)setDragOffsetX:(CGFloat)offset;
/// Set an offset in pixels that allows the user to drag the chart over it’s bounds on the y-axis.
- (void)setDragOffsetY:(CGFloat)offset;
/// <code>true</code> if both drag offsets (x and y) are zero or smaller.
@property (nonatomic, readonly) BOOL hasNoDragOffset;
/// <code>true</code> if the chart is not yet fully zoomed out on the x-axis
@property (nonatomic, readonly) BOOL canZoomOutMoreX;
/// <code>true</code> if the chart is not yet fully zoomed in on the x-axis
@property (nonatomic, readonly) BOOL canZoomInMoreX;
/// <code>true</code> if the chart is not yet fully zoomed out on the y-axis
@property (nonatomic, readonly) BOOL canZoomOutMoreY;
/// <code>true</code> if the chart is not yet fully zoomed in on the y-axis
@property (nonatomic, readonly) BOOL canZoomInMoreY;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

enum XAxisLabelPosition : NSInteger;
SWIFT_CLASS_NAMED("XAxis")
@interface ChartXAxis : ChartAxisBase
/// width of the x-axis labels in pixels - this is automatically calculated by the <code>computeSize()</code> methods in the renderers
@property (nonatomic) CGFloat labelWidth;
/// height of the x-axis labels in pixels - this is automatically calculated by the <code>computeSize()</code> methods in the renderers
@property (nonatomic) CGFloat labelHeight;
/// width of the (rotated) x-axis labels in pixels - this is automatically calculated by the <code>computeSize()</code> methods in the renderers
@property (nonatomic) CGFloat labelRotatedWidth;
/// height of the (rotated) x-axis labels in pixels - this is automatically calculated by the <code>computeSize()</code> methods in the renderers
@property (nonatomic) CGFloat labelRotatedHeight;
/// This is the angle for drawing the X axis labels (in degrees)
@property (nonatomic) CGFloat labelRotationAngle;
/// if set to true, the chart will avoid that the first and last label entry in the chart “clip” off the edge of the chart
@property (nonatomic) BOOL avoidFirstLastClippingEnabled;
/// the position of the x-labels relative to the chart
@property (nonatomic) enum XAxisLabelPosition labelPosition;
/// if set to true, word wrapping the labels will be enabled.
/// word wrapping is done using <code>(value width * labelRotatedWidth)</code>
/// note:
/// currently supports all charts except pie/radar/horizontal-bar*
@property (nonatomic) BOOL wordWrapEnabled;
/// <code>true</code> if word wrapping the labels is enabled
@property (nonatomic, readonly) BOOL isWordWrapEnabled;
/// the width for wrapping the labels, as percentage out of one value width.
/// used only when isWordWrapEnabled = true.
/// <em>default</em>: 1.0
@property (nonatomic) CGFloat wordWrapWidthPercent;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, readonly) BOOL isAvoidFirstLastClippingEnabled;
@end

typedef SWIFT_ENUM_NAMED(NSInteger, XAxisLabelPosition, "LabelPosition", closed) {
  XAxisLabelPositionTop = 0,
  XAxisLabelPositionBottom = 1,
  XAxisLabelPositionBothSided = 2,
  XAxisLabelPositionTopInside = 3,
  XAxisLabelPositionBottomInside = 4,
};

SWIFT_CLASS_NAMED("XAxisRenderer")
@interface ChartXAxisRenderer : NSObject
@property (nonatomic, readonly, strong) ChartViewPortHandler * _Nonnull viewPortHandler;
@property (nonatomic, readonly, strong) ChartXAxis * _Nonnull axis;
@property (nonatomic, readonly, strong) ChartTransformer * _Nullable transformer;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler axis:(ChartXAxis * _Nonnull)axis transformer:(ChartTransformer * _Nullable)transformer OBJC_DESIGNATED_INITIALIZER;
- (void)computeSize;
/// draws the x-labels on the specified y-position
- (void)drawLabelsWithContext:(CGContextRef _Nonnull)context pos:(CGFloat)pos anchor:(CGPoint)anchor;
- (void)drawLabelWithContext:(CGContextRef _Nonnull)context formattedLabel:(NSString * _Nonnull)formattedLabel x:(CGFloat)x y:(CGFloat)y attributes:(NSDictionary<NSAttributedStringKey, id> * _Nonnull)attributes constrainedTo:(CGSize)size anchor:(CGPoint)anchor angleRadians:(CGFloat)angleRadians;
@property (nonatomic, readonly) CGRect gridClippingRect;
- (void)drawGridLineWithContext:(CGContextRef _Nonnull)context x:(CGFloat)x y:(CGFloat)y;
- (void)renderLimitLineLineWithContext:(CGContextRef _Nonnull)context limitLine:(ChartLimitLine * _Nonnull)limitLine position:(CGPoint)position;
- (void)renderLimitLineLabelWithContext:(CGContextRef _Nonnull)context limitLine:(ChartLimitLine * _Nonnull)limitLine position:(CGPoint)position yOffset:(CGFloat)yOffset;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS("_TtC8DGCharts31XAxisRendererHorizontalBarChart")
@interface XAxisRendererHorizontalBarChart : ChartXAxisRenderer
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler axis:(ChartXAxis * _Nonnull)axis transformer:(ChartTransformer * _Nullable)transformer chart:(BarChartView * _Nonnull)chart OBJC_DESIGNATED_INITIALIZER;
- (void)computeSize;
/// draws the x-labels on the specified y-position
- (void)drawLabelsWithContext:(CGContextRef _Nonnull)context pos:(CGFloat)pos anchor:(CGPoint)anchor;
- (void)drawLabelWithContext:(CGContextRef _Nonnull)context formattedLabel:(NSString * _Nonnull)formattedLabel x:(CGFloat)x y:(CGFloat)y attributes:(NSDictionary<NSAttributedStringKey, id> * _Nonnull)attributes anchor:(CGPoint)anchor angleRadians:(CGFloat)angleRadians;
@property (nonatomic, readonly) CGRect gridClippingRect;
- (void)drawGridLineWithContext:(CGContextRef _Nonnull)context x:(CGFloat)x y:(CGFloat)y;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler axis:(ChartXAxis * _Nonnull)axis transformer:(ChartTransformer * _Nullable)transformer SWIFT_UNAVAILABLE;
@end

SWIFT_CLASS("_TtC8DGCharts23XAxisRendererRadarChart")
@interface XAxisRendererRadarChart : ChartXAxisRenderer
@property (nonatomic, weak) RadarChartView * _Nullable chart;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler axis:(ChartXAxis * _Nonnull)axis chart:(RadarChartView * _Nonnull)chart OBJC_DESIGNATED_INITIALIZER;
- (void)drawLabelWithContext:(CGContextRef _Nonnull)context formattedLabel:(NSString * _Nonnull)formattedLabel x:(CGFloat)x y:(CGFloat)y attributes:(NSDictionary<NSAttributedStringKey, id> * _Nonnull)attributes anchor:(CGPoint)anchor angleRadians:(CGFloat)angleRadians;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler axis:(ChartXAxis * _Nonnull)axis transformer:(ChartTransformer * _Nullable)transformer SWIFT_UNAVAILABLE;
@end

SWIFT_CLASS("_TtC8DGCharts14XShapeRenderer")
@interface XShapeRenderer : NSObject <ShapeRenderer>
- (void)renderShapeWithContext:(CGContextRef _Nonnull)context dataSet:(id <ScatterChartDataSetProtocol> _Nonnull)dataSet viewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler point:(CGPoint)point color:(UIColor * _Nonnull)color;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

enum YAxisLabelPosition : NSInteger;
/// Class representing the y-axis labels settings and its entries.
/// Be aware that not all features the YLabels class provides are suitable for the RadarChart.
/// Customizations that affect the value range of the axis need to be applied before setting data for the chart.
SWIFT_CLASS_NAMED("YAxis")
@interface ChartYAxis : ChartAxisBase
/// indicates if the bottom y-label entry is drawn or not
@property (nonatomic) BOOL drawBottomYLabelEntryEnabled;
/// indicates if the top y-label entry is drawn or not
@property (nonatomic) BOOL drawTopYLabelEntryEnabled;
/// flag that indicates if the axis is inverted or not
@property (nonatomic) BOOL inverted;
/// flag that indicates if the zero-line should be drawn regardless of other grid lines
@property (nonatomic) BOOL drawZeroLineEnabled;
/// Color of the zero line
@property (nonatomic, strong) UIColor * _Nullable zeroLineColor;
/// Width of the zero line
@property (nonatomic) CGFloat zeroLineWidth;
/// This is how much (in pixels) into the dash pattern are we starting from.
@property (nonatomic) CGFloat zeroLineDashPhase;
/// This is the actual dash pattern.
/// I.e. [2, 3] will paint [–   –   ]
/// [1, 3, 4, 2] will paint [-   ––  -   ––  ]
@property (nonatomic, copy) NSArray<NSNumber *> * _Nullable zeroLineDashLengths;
/// axis space from the largest value to the top in percent of the total axis range
@property (nonatomic) CGFloat spaceTop;
/// axis space from the smallest value to the bottom in percent of the total axis range
@property (nonatomic) CGFloat spaceBottom;
/// the position of the y-labels relative to the chart
@property (nonatomic) enum YAxisLabelPosition labelPosition;
/// the alignment of the text in the y-label
@property (nonatomic) NSTextAlignment labelAlignment;
/// the horizontal offset of the y-label
@property (nonatomic) CGFloat labelXOffset;
/// the minimum width that the axis should take
/// <em>default</em>: 0.0
@property (nonatomic) CGFloat minWidth;
/// the maximum width that the axis can take.
/// use Infinity for disabling the maximum.
/// <em>default</em>: CGFloat.infinity
@property (nonatomic) CGFloat maxWidth;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithPosition:(enum AxisDependency)position OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, readonly) enum AxisDependency axisDependency;
- (CGSize)requiredSize SWIFT_WARN_UNUSED_RESULT;
- (CGFloat)getRequiredHeightSpace SWIFT_WARN_UNUSED_RESULT;
/// <code>true</code> if this axis needs horizontal offset, <code>false</code> ifno offset is needed.
@property (nonatomic, readonly) BOOL needsOffset;
@property (nonatomic, readonly) BOOL isInverted;
- (void)calculateWithMin:(double)dataMin max:(double)dataMax;
@property (nonatomic, readonly) BOOL isDrawBottomYLabelEntryEnabled;
@property (nonatomic, readonly) BOOL isDrawTopYLabelEntryEnabled;
@end

typedef SWIFT_ENUM_NAMED(NSInteger, YAxisLabelPosition, "LabelPosition", closed) {
  YAxisLabelPositionOutsideChart = 0,
  YAxisLabelPositionInsideChart = 1,
};

/// Enum that specifies the axis a DataSet should be plotted against, either Left or Right.
typedef SWIFT_ENUM(NSInteger, AxisDependency, closed) {
  AxisDependencyLeft = 0,
  AxisDependencyRight = 1,
};

SWIFT_CLASS_NAMED("YAxisRenderer")
@interface ChartYAxisRenderer : NSObject
@property (nonatomic, readonly, strong) ChartViewPortHandler * _Nonnull viewPortHandler;
@property (nonatomic, readonly, strong) ChartYAxis * _Nonnull axis;
@property (nonatomic, readonly, strong) ChartTransformer * _Nullable transformer;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler axis:(ChartYAxis * _Nonnull)axis transformer:(ChartTransformer * _Nullable)transformer OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, readonly) CGRect gridClippingRect;
- (void)drawGridLineWithContext:(CGContextRef _Nonnull)context position:(CGPoint)position;
- (NSArray<NSValue *> * _Nonnull)transformedPositions SWIFT_WARN_UNUSED_RESULT;
/// Draws the zero line at the specified position.
- (void)drawZeroLineWithContext:(CGContextRef _Nonnull)context;
- (void)computeAxisWithMin:(double)min max:(double)max inverted:(BOOL)inverted;
- (void)computeAxisValuesWithMin:(double)min max:(double)max;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS("_TtC8DGCharts31YAxisRendererHorizontalBarChart")
@interface YAxisRendererHorizontalBarChart : ChartYAxisRenderer
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler axis:(ChartYAxis * _Nonnull)axis transformer:(ChartTransformer * _Nullable)transformer OBJC_DESIGNATED_INITIALIZER;
/// Computes the axis values.
- (void)computeAxisWithMin:(double)min max:(double)max inverted:(BOOL)inverted;
/// draws the y-labels on the specified x-position
- (void)drawYLabelsWithContext:(CGContextRef _Nonnull)context fixedPosition:(CGFloat)fixedPosition positions:(NSArray<NSValue *> * _Nonnull)positions offset:(CGFloat)offset;
@property (nonatomic, readonly) CGRect gridClippingRect;
- (void)drawGridLineWithContext:(CGContextRef _Nonnull)context position:(CGPoint)position;
- (NSArray<NSValue *> * _Nonnull)transformedPositions SWIFT_WARN_UNUSED_RESULT;
/// Draws the zero line at the specified position.
- (void)drawZeroLineWithContext:(CGContextRef _Nonnull)context;
@end

SWIFT_CLASS("_TtC8DGCharts23YAxisRendererRadarChart")
@interface YAxisRendererRadarChart : ChartYAxisRenderer
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler axis:(ChartYAxis * _Nonnull)axis chart:(RadarChartView * _Nonnull)chart OBJC_DESIGNATED_INITIALIZER;
- (void)computeAxisValuesWithMin:(double)yMin max:(double)yMax;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler axis:(ChartYAxis * _Nonnull)axis transformer:(ChartTransformer * _Nullable)transformer SWIFT_UNAVAILABLE;
@end

SWIFT_CLASS_NAMED("ZoomViewJob")
@interface ZoomChartViewJob : ChartViewPortJob
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler scaleX:(CGFloat)scaleX scaleY:(CGFloat)scaleY xValue:(double)xValue yValue:(double)yValue transformer:(ChartTransformer * _Nonnull)transformer axis:(enum AxisDependency)axis view:(ChartViewBase * _Nonnull)view OBJC_DESIGNATED_INITIALIZER;
- (void)doJob;
- (nonnull instancetype)initWithViewPortHandler:(ChartViewPortHandler * _Nonnull)viewPortHandler xValue:(double)xValue yValue:(double)yValue transformer:(ChartTransformer * _Nonnull)transformer view:(ChartViewBase * _Nonnull)view SWIFT_UNAVAILABLE;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
