#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 6.1.2 effective-5.10 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)
#ifndef EXUPDATES_SWIFT_H
#define EXUPDATES_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef unsigned char char8_t;
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import Dispatch;
@import EXUpdatesInterface;
@import ExpoModulesCore;
@import Foundation;
@import ObjectiveC;
@import UIKit;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="EXUpdates",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

@class NSString;
/// Main entry point to expo-updates. Singleton that keeps track of updates state, holds references
/// to instances of other updates classes, and is the central hub for all updates-related tasks.
/// The <code>start</code> method in the singleton instance of [IUpdatesController] should be invoked early in
/// the application lifecycle, via [UpdatesPackage]. It delegates to an instance of [LoaderTask] to
/// start the process of loading and launching an update, then responds appropriately depending on
/// the callbacks that are invoked.
/// This class also optionally holds a reference to the app’s [ReactNativeHost], which allows
/// expo-updates to reload JS and send events through the bridge.
SWIFT_CLASS_NAMED("AppController")
@interface EXUpdatesAppController : NSObject
+ (BOOL)isInitialized SWIFT_WARN_UNUSED_RESULT;
+ (void)initializeWithoutStarting;
+ (void)overrideConfigurationWithConfiguration:(NSDictionary<NSString *, id> * _Nullable)configuration;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@protocol EXUpdatesAppControllerInterface;
SWIFT_PROTOCOL_NAMED("AppControllerDelegate")
@protocol EXUpdatesAppControllerDelegate
- (void)appController:(id <EXUpdatesAppControllerInterface> _Nonnull)appController didStartWithSuccess:(BOOL)success;
@end

@class NSURL;
SWIFT_PROTOCOL_NAMED("AppControllerInterface")
@protocol EXUpdatesAppControllerInterface
/// Delegate which will be notified when EXUpdates has an update ready to launch and
/// <code>launchAssetUrl</code> is nonnull.
@property (nonatomic, weak) id <EXUpdatesAppControllerDelegate> _Nullable delegate;
/// The URL on disk to source asset for the RCTBridge.
/// Will be null until the AppController delegate method is called.
/// This should be provided in the <code>sourceURLForBridge:</code> method of RCTBridgeDelegate.
- (NSURL * _Nullable)launchAssetUrl SWIFT_WARN_UNUSED_RESULT;
/// Indicates that the controller is in active state.
/// Currently it’s only active for <code>EnabledAppController</code>.
@property (nonatomic, readonly) BOOL isActiveController;
/// Starts the update process to launch a previously-loaded update and (if configured to do so)
/// check for a new update from the server. This method should be called as early as possible in
/// the application’s lifecycle.
/// Note that iOS may stop showing the app’s splash screen in case the update is taking a while
/// to load. If your splash screen setup is simple, you may want to use the
/// <code>startAndShowLaunchScreen:</code> method instead.
- (void)start;
@end

@class EXUpdatesUpdate;
/// Protocol through which an update can be launched from disk. Classes that implement this protocol
/// are responsible for selecting an eligible update to launch, ensuring all required assets are
/// present, and providing the fields here.
SWIFT_PROTOCOL_NAMED("AppLauncher")
@protocol EXUpdatesAppLauncher
@property (nonatomic, readonly, strong) EXUpdatesUpdate * _Nullable launchedUpdate;
@property (nonatomic, readonly, copy) NSURL * _Nullable launchAssetUrl;
@property (nonatomic, readonly, copy) NSDictionary<NSString *, NSString *> * _Nullable assetFilesMap;
- (BOOL)isUsingEmbeddedAssets SWIFT_WARN_UNUSED_RESULT;
@end

/// Implementation of AppLauncher which always uses the update embedded in the application
/// package, avoiding SQLite and the expo-updates file store entirely.
/// This is only used in rare cases when the database/file system is corrupt or otherwise
/// inaccessible, but we still want to avoid crashing. The exported property <code>isEmergencyLaunch</code> on
/// UpdatesModule should be <code>true</code> whenever this class is used.
SWIFT_CLASS_NAMED("AppLauncherNoDatabase")
@interface EXUpdatesAppLauncherNoDatabase : NSObject <EXUpdatesAppLauncher>
@property (nonatomic, strong) EXUpdatesUpdate * _Nullable launchedUpdate;
@property (nonatomic, copy) NSURL * _Nullable launchAssetUrl;
@property (nonatomic, copy) NSDictionary<NSString *, NSString *> * _Nullable assetFilesMap;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (void)launchUpdate;
- (BOOL)isUsingEmbeddedAssets SWIFT_WARN_UNUSED_RESULT;
@end

@class EXUpdatesConfig;
@class EXUpdatesDatabase;
/// Implementation of AppLauncher that uses the SQLite database and expo-updates file store
/// as the source of updates.
/// Uses the SelectionPolicy to choose an update from SQLite to launch, then ensures that
/// the update is safe and ready to launch (i.e. all the assets that SQLite expects to be stored on
/// disk are actually there).
/// This class also includes failsafe code to attempt to re-download any assets unexpectedly missing
/// from disk (since it isn’t necessarily safe to just revert to an older update in this case).
/// Distinct from the AppLoader classes, though, this class does <em>not</em> make any major
/// modifications to the database; its role is mostly to read the database and ensure integrity with
/// the file system.
/// It’s important that the update to launch is selected <em>before</em> any other checks, e.g. the above
/// check for assets on disk. This is to preserve the invariant that no older update should ever be
/// launched after a newer one has been launched.
SWIFT_CLASS_NAMED("AppLauncherWithDatabase")
@interface EXUpdatesAppLauncherWithDatabase : NSObject <EXUpdatesAppLauncher>
@property (nonatomic, strong) EXUpdatesUpdate * _Nullable launchedUpdate;
@property (nonatomic, copy) NSURL * _Nullable launchAssetUrl;
@property (nonatomic, copy) NSDictionary<NSString *, NSString *> * _Nullable assetFilesMap;
@property (nonatomic, readonly, strong) dispatch_queue_t _Nonnull completionQueue;
- (nonnull instancetype)initWithConfig:(EXUpdatesConfig * _Nonnull)config database:(EXUpdatesDatabase * _Nonnull)database directory:(NSURL * _Nonnull)directory completionQueue:(dispatch_queue_t _Nonnull)completionQueue OBJC_DESIGNATED_INITIALIZER;
- (BOOL)isUsingEmbeddedAssets SWIFT_WARN_UNUSED_RESULT;
- (void)ensureAllAssetsExist;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class EXUpdatesAsset;
@class NSData;
@class NSURLResponse;
/// Responsible for loading an update’s manifest, enumerating the assets required for it to launch,
/// and loading them all onto disk and into SQLite.
/// There are two sources from which an update can be loaded - a remote server given a URL, and the
/// application package. These correspond to the two loader subclasses.
SWIFT_CLASS_NAMED("AppLoader")
@interface EXUpdatesAppLoader : NSObject
@property (nonatomic, readonly, strong) EXUpdatesConfig * _Nonnull config;
@property (nonatomic, readonly, strong) EXUpdatesDatabase * _Nonnull database;
@property (nonatomic, readonly, copy) NSURL * _Nonnull directory;
@property (nonatomic, readonly, strong) EXUpdatesUpdate * _Nullable launchedUpdate;
@property (nonatomic, copy) void (^ _Nullable assetBlock)(EXUpdatesAsset * _Nonnull, NSInteger, NSInteger, NSInteger);
- (void)reset;
- (void)downloadAsset:(EXUpdatesAsset * _Nonnull)asset extraHeaders:(NSDictionary<NSString *, id> * _Nonnull)extraHeaders;
- (void)handleAssetDownloadAlreadyExists:(EXUpdatesAsset * _Nonnull)asset;
- (void)handleAssetDownloadWithData:(NSData * _Nonnull)data response:(NSURLResponse * _Nullable)response asset:(EXUpdatesAsset * _Nonnull)asset;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@protocol EXUpdatesAppLoaderTaskDelegate;
@class EXUpdatesSelectionPolicy;
/// Controlling class that handles the complex logic that needs to happen each time the app is cold
/// booted. From a high level, this class does the following:
/// <ul>
///   <li>
///     Immediately starts an instance of EmbeddedAppLoader to load the embedded update into
///     SQLite. This does nothing if SQLite already has the embedded update or a newer one, but we have
///     to do this on each cold boot, as we have no way of knowing if a new build was just installed
///     (which could have a new embedded update).
///   </li>
///   <li>
///     If the app is configured for automatic update downloads (most apps), starts a timer based on
///     the <code>launchWaitMs</code> value in UpdatesConfig.
///   </li>
///   <li>
///     Again if the app is configured for automatic update downloads, starts an instance of
///     RemoteAppLoader to check for and download a new update if there is one.
///   </li>
///   <li>
///     Once the download succeeds, fails, or the timer runs out (whichever happens first), creates an
///     instance of AppLauncherWithDatabase and signals that the app is ready to be launched
///     with the newest update available locally at that time (which may not be the newest update if
///     the download is still in progress).
///   </li>
///   <li>
///     If the download succeeds or fails after this point, fires a callback which causes an event to
///     be sent to JS.
///   </li>
/// </ul>
SWIFT_CLASS_NAMED("AppLoaderTask")
@interface EXUpdatesAppLoaderTask : NSObject
@property (nonatomic, weak) id <EXUpdatesAppLoaderTaskDelegate> _Nullable delegate;
@property (nonatomic, readonly) BOOL isRunning;
- (nonnull instancetype)initWithConfig:(EXUpdatesConfig * _Nonnull)config database:(EXUpdatesDatabase * _Nonnull)database directory:(NSURL * _Nonnull)directory selectionPolicy:(EXUpdatesSelectionPolicy * _Nonnull)selectionPolicy delegateQueue:(dispatch_queue_t _Nonnull)delegateQueue OBJC_DESIGNATED_INITIALIZER;
- (void)start;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

enum EXUpdatesBackgroundUpdateStatus : NSInteger;
SWIFT_PROTOCOL_NAMED("AppLoaderTaskDelegate")
@protocol EXUpdatesAppLoaderTaskDelegate
/// This method gives the delegate a backdoor option to ignore the cached update and force
/// a remote load if it decides the cached update is not runnable. Returning NO from this
/// callback will force a remote load, overriding the timeout and configuration settings for
/// whether or not to check for a remote update. Returning YES from this callback will make
/// AppLoaderTask proceed as usual.
- (BOOL)appLoaderTask:(EXUpdatesAppLoaderTask * _Nonnull)_ didLoadCachedUpdate:(EXUpdatesUpdate * _Nonnull)update SWIFT_WARN_UNUSED_RESULT;
- (void)appLoaderTask:(EXUpdatesAppLoaderTask * _Nonnull)_ didStartLoadingUpdate:(EXUpdatesUpdate * _Nullable)update;
- (void)appLoaderTask:(EXUpdatesAppLoaderTask * _Nonnull)_ didFinishWithLauncher:(id <EXUpdatesAppLauncher> _Nonnull)launcher isUpToDate:(BOOL)isUpToDate;
- (void)appLoaderTask:(EXUpdatesAppLoaderTask * _Nonnull)_ didFinishWithError:(NSError * _Nonnull)error;
- (void)appLoaderTask:(EXUpdatesAppLoaderTask * _Nonnull)_ didFinishBackgroundUpdateWithStatus:(enum EXUpdatesBackgroundUpdateStatus)status update:(EXUpdatesUpdate * _Nullable)update error:(NSError * _Nullable)error;
/// This method is called after the loader task finishes doing all work. Note that it may have
/// “succeeded” before this with a loader, yet this method may still be called after the launch
/// to signal that all work is done (loading a remote update after the launch wait timeout has occurred).
- (void)appLoaderTaskDidFinishAllLoading:(EXUpdatesAppLoaderTask * _Nonnull)_;
@end

typedef SWIFT_ENUM_NAMED(NSInteger, EXUpdatesBackgroundUpdateStatus, "BackgroundUpdateStatus", closed) {
  EXUpdatesBackgroundUpdateStatusError = 0,
  EXUpdatesBackgroundUpdateStatusNoUpdateAvailable = 1,
  EXUpdatesBackgroundUpdateStatusUpdateAvailable = 2,
};

typedef SWIFT_ENUM_NAMED(NSInteger, EXUpdatesCheckAutomaticallyConfig, "CheckAutomaticallyConfig", closed) {
  EXUpdatesCheckAutomaticallyConfigAlways = 0,
  EXUpdatesCheckAutomaticallyConfigWifiOnly = 1,
  EXUpdatesCheckAutomaticallyConfigNever = 2,
  EXUpdatesCheckAutomaticallyConfigErrorRecoveryOnly = 3,
};

SWIFT_CLASS_NAMED("CodeSigningConfiguration")
@interface EXUpdatesCodeSigningConfiguration : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@protocol EXUpdatesExternalInterfaceDelegate;
/// Main entry point to expo-updates in development builds with expo-dev-client. Similar to EnabledUpdatesController
/// in that it keeps track of updates state, but provides capabilities that are not usually exposed but
/// that expo-dev-client needs (launching and downloading a specific
/// update by URL, allowing dynamic configuration, introspecting the database). The behavior of this
/// class differs enough that it is implemented independently from EnabledUpdatesController.
/// Implements the external UpdatesInterface from the expo-updates-interface package. This allows
/// expo-dev-client to compile without needing expo-updates to be installed.
SWIFT_CLASS_NAMED("DevLauncherAppController")
@interface EXUpdatesDevLauncherController : NSObject <EXUpdatesExternalInterface>
@property (nonatomic, weak) id <EXUpdatesAppControllerDelegate> _Nullable delegate;
@property (nonatomic, weak) id <EXUpdatesExternalInterfaceDelegate> _Nullable updatesExternalInterfaceDelegate;
- (NSURL * _Nullable)launchAssetUrl SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, readonly, copy) NSURL * _Nullable launchAssetURL;
@property (nonatomic, readonly, copy) NSString * _Nullable runtimeVersion;
@property (nonatomic, readonly, copy) NSURL * _Nullable updateURL;
- (void)start;
@property (nonatomic, readonly, copy) NSURL * _Nullable updatesDirectory;
@property (nonatomic, readonly) BOOL isActiveController;
- (NSDictionary<NSString *, id> * _Nullable)assetFilesMap SWIFT_WARN_UNUSED_RESULT;
- (BOOL)isUsingEmbeddedAssets SWIFT_WARN_UNUSED_RESULT;
- (void)onEventListenerStartObserving;
- (void)reset;
- (void)fetchUpdateWithConfiguration:(NSDictionary<NSString *, id> * _Nonnull)configuration onManifest:(BOOL (^ _Nonnull)(NSDictionary<NSString *, id> * _Nonnull))manifestBlock progress:(void (^ _Nonnull)(NSUInteger, NSUInteger, NSUInteger))progressBlock success:(void (^ _Nonnull)(NSDictionary<NSString *, id> * _Nullable))successBlock error:(void (^ _Nonnull)(NSError * _Nonnull))errorBlock;
- (BOOL)isValidUpdatesConfiguration:(NSDictionary<NSString *, id> * _Nonnull)configuration SWIFT_WARN_UNUSED_RESULT;
- (EXUpdatesSelectionPolicy * _Nonnull)selectionPolicy SWIFT_WARN_UNUSED_RESULT;
- (void)setNextSelectionPolicy:(EXUpdatesSelectionPolicy * _Nonnull)nextSelectionPolicy;
- (void)resetSelectionPolicyToDefault;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class EmbeddedUpdate;
/// Subclass of AppLoader which handles copying the embedded update’s assets into the
/// expo-updates cache location.
/// Rather than launching the embedded update directly from its location in the app bundle/apk, we
/// first try to read it into the expo-updates cache and database and launch it like any other
/// update. The benefits of this include (a) a single code path for launching most updates and (b)
/// assets included in embedded updates and copied into the cache in this way do not need to be
/// redownloaded if included in future updates.
SWIFT_CLASS_NAMED("EmbeddedAppLoader")
@interface EXUpdatesEmbeddedAppLoader : EXUpdatesAppLoader
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesEmbeddedManifestName;)
+ (NSString * _Nonnull)EXUpdatesEmbeddedManifestName SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesEmbeddedManifestType;)
+ (NSString * _Nonnull)EXUpdatesEmbeddedManifestType SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesEmbeddedBundleFilename;)
+ (NSString * _Nonnull)EXUpdatesEmbeddedBundleFilename SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesEmbeddedBundleFileType;)
+ (NSString * _Nonnull)EXUpdatesEmbeddedBundleFileType SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesBareEmbeddedBundleFilename;)
+ (NSString * _Nonnull)EXUpdatesBareEmbeddedBundleFilename SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesBareEmbeddedBundleFileType;)
+ (NSString * _Nonnull)EXUpdatesBareEmbeddedBundleFileType SWIFT_WARN_UNUSED_RESULT;
+ (EmbeddedUpdate * _Nullable)embeddedManifestWithConfig:(EXUpdatesConfig * _Nonnull)config database:(EXUpdatesDatabase * _Nullable)database SWIFT_WARN_UNUSED_RESULT;
- (void)downloadAsset:(EXUpdatesAsset * _Nonnull)asset extraHeaders:(NSDictionary<NSString *, id> * _Nonnull)extraHeaders;
@end

@class NSUUID;
@class NSDate;
@class EXManifestsManifest;
enum EXUpdatesUpdateStatus : NSInteger;
SWIFT_CLASS_NAMED("Update")
@interface EXUpdatesUpdate : NSObject
@property (nonatomic, readonly, copy) NSUUID * _Nonnull updateId;
@property (nonatomic, readonly, copy) NSString * _Nullable scopeKey;
@property (nonatomic, copy) NSDate * _Nonnull commitTime;
@property (nonatomic, readonly, copy) NSString * _Nonnull runtimeVersion;
@property (nonatomic, readonly) BOOL keep;
@property (nonatomic, readonly) BOOL isDevelopmentMode;
@property (nonatomic, readonly, strong) EXManifestsManifest * _Nonnull manifest;
@property (nonatomic) enum EXUpdatesUpdateStatus status;
@property (nonatomic, copy) NSDate * _Nonnull lastAccessed;
@property (nonatomic) NSInteger successfulLaunchCount;
@property (nonatomic) NSInteger failedLaunchCount;
- (nonnull instancetype)initWithManifest:(EXManifestsManifest * _Nonnull)manifest config:(EXUpdatesConfig * _Nonnull)config database:(EXUpdatesDatabase * _Nullable)database updateId:(NSUUID * _Nonnull)updateId scopeKey:(NSString * _Nullable)scopeKey commitTime:(NSDate * _Nonnull)commitTime runtimeVersion:(NSString * _Nonnull)runtimeVersion keep:(BOOL)keep status:(enum EXUpdatesUpdateStatus)status isDevelopmentMode:(BOOL)isDevelopmentMode assetsFromManifest:(NSArray<EXUpdatesAsset *> * _Nullable)assetsFromManifest OBJC_DESIGNATED_INITIALIZER;
+ (EmbeddedUpdate * _Nonnull)updateWithRawEmbeddedManifest:(NSDictionary<NSString *, id> * _Nonnull)withRawEmbeddedManifest config:(EXUpdatesConfig * _Nonnull)config database:(EXUpdatesDatabase * _Nullable)database SWIFT_WARN_UNUSED_RESULT;
/// Accessing this property may lazily load the assets from the database, if this update object
/// originated from the database.
- (NSArray<EXUpdatesAsset *> * _Nullable)assets SWIFT_WARN_UNUSED_RESULT;
- (NSString * _Nonnull)loggingId SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class EXManifestsEmbeddedManifest;
SWIFT_CLASS("_TtC9EXUpdates14EmbeddedUpdate")
@interface EmbeddedUpdate : EXUpdatesUpdate
/// Method for initializing updates from the bare-bones-styles manifests embedded in application
/// binaries. These manifest objects are generated by the script at
/// expo-updates/scripts/createManifest.js and describe the update embedded by react-native in the
/// application binary. They contain the minimum amount of information needed to reliably identify
/// the update and insert it into SQLite.
+ (EmbeddedUpdate * _Nonnull)updateWithEmbeddedManifest:(EXManifestsEmbeddedManifest * _Nonnull)withEmbeddedManifest config:(EXUpdatesConfig * _Nonnull)config database:(EXUpdatesDatabase * _Nullable)database SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithManifest:(EXManifestsManifest * _Nonnull)manifest config:(EXUpdatesConfig * _Nonnull)config database:(EXUpdatesDatabase * _Nullable)database updateId:(NSUUID * _Nonnull)updateId scopeKey:(NSString * _Nullable)scopeKey commitTime:(NSDate * _Nonnull)commitTime runtimeVersion:(NSString * _Nonnull)runtimeVersion keep:(BOOL)keep status:(enum EXUpdatesUpdateStatus)status isDevelopmentMode:(BOOL)isDevelopmentMode assetsFromManifest:(NSArray<EXUpdatesAsset *> * _Nullable)assetsFromManifest OBJC_DESIGNATED_INITIALIZER;
@end

@class NSError;
@class NSException;
enum EXUpdatesRemoteLoadStatus : NSInteger;
/// Entry point and main handler for the error recovery flow. Responsible for initializing the error
/// recovery handler and handler thread, and for registering (and unregistering) listeners to
/// lifecycle events so that the appropriate error recovery flows will be triggered.
/// Also keeps track of and executes tasks in the error recovery pipeline, which allows us to
/// predictably and serially respond to unpredictably ordered events.
/// This error recovery flow is intended to be lightweight and is <em>not</em> a full safety net whose
/// purpose is to avoid crashes at all costs. Rather, its primary purpose is to prevent bad updates
/// from “bricking” an app by causing crashes before there is ever a chance to download a fix.
/// When an error is caught, the pipeline is started and executes the following tasks serially:
/// (a) check for a new update and start downloading if there is one
/// (b) if there is a new update, reload and launch the new update
/// (c) if not, or if another error occurs, fall back to an older working update (if one exists)
/// (d) crash.
/// Importantly, (b) and (c) will be taken out of the pipeline as soon as the first root view render
/// occurs. If any update modifies persistent state in a non-backwards-compatible way, it isn’t
/// safe to automatically roll back; we use the first root view render as a rough proxy for this
/// (assuming it’s unlikely an app will make significant modifications to persisted state before its
/// initial render).
/// Also, the error listener will be unregistered 10 seconds after content has appeared; we assume
/// that by this point, expo-updates has had enough time to download a new update if there is one,
/// and so there is no more need to trigger the error recovery pipeline.
/// This pipeline will not be triggered at all for errors caught more than 10 seconds after content
/// has appeared; it is assumed that by this point, expo-updates will have had enough time to
/// download a new update if there is one, and so there is no more need to intervene.
/// This behavior is documented in more detail at https://docs.expo.dev/bare/error-recovery/.
SWIFT_CLASS_NAMED("ErrorRecovery")
@interface EXUpdatesErrorRecovery : NSObject
- (nonnull instancetype)init;
- (nonnull instancetype)initWithErrorRecoveryQueue:(dispatch_queue_t _Nonnull)errorRecoveryQueue remoteLoadTimeout:(NSInteger)remoteLoadTimeout OBJC_DESIGNATED_INITIALIZER;
- (void)startMonitoring;
- (void)handleWithError:(NSError * _Nonnull)error;
- (void)handleWithException:(NSException * _Nonnull)exception;
- (void)notifyWithNewRemoteLoadStatus:(enum EXUpdatesRemoteLoadStatus)newStatus;
@end

@class EXReactDelegate;
@class UIView;
/// Manages and controls the auto-setup behavior of expo-updates in applicable environments.
/// In order to deal with the asynchronous nature of updates startup, this class creates dummy
/// RCTBridge and RCTRootView objects to return to the ReactDelegate, replacing them with the real
/// objects when expo-updates is ready.
SWIFT_CLASS("_TtC9EXUpdates31ExpoUpdatesReactDelegateHandler")
@interface ExpoUpdatesReactDelegateHandler : EXReactDelegateHandler <EXUpdatesAppControllerDelegate>
- (UIView * _Nullable)createReactRootViewWithReactDelegate:(EXReactDelegate * _Nonnull)reactDelegate moduleName:(NSString * _Nonnull)moduleName initialProperties:(NSDictionary * _Nullable)initialProperties launchOptions:(NSDictionary<UIApplicationLaunchOptionsKey, id> * _Nullable)launchOptions SWIFT_WARN_UNUSED_RESULT;
- (NSURL * _Nullable)bundleURLWithReactDelegate:(EXReactDelegate * _Nonnull)reactDelegate SWIFT_WARN_UNUSED_RESULT;
- (void)appController:(id <EXUpdatesAppControllerInterface> _Nonnull)appController didStartWithSuccess:(BOOL)success;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

@class EXManifestsExpoUpdatesManifest;
SWIFT_CLASS("_TtC9EXUpdates17ExpoUpdatesUpdate")
@interface ExpoUpdatesUpdate : EXUpdatesUpdate
/// Method for initializing updates with modern format manifests that conform to the Expo Updates
/// specification (https://docs.expo.dev/technical-specs/expo-updates-1/). This is used by EAS
/// Update.
+ (EXUpdatesUpdate * _Nonnull)updateWithExpoUpdatesManifest:(EXManifestsExpoUpdatesManifest * _Nonnull)withExpoUpdatesManifest extensions:(NSDictionary<NSString *, id> * _Nonnull)extensions config:(EXUpdatesConfig * _Nonnull)config database:(EXUpdatesDatabase * _Nonnull)database SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithManifest:(EXManifestsManifest * _Nonnull)manifest config:(EXUpdatesConfig * _Nonnull)config database:(EXUpdatesDatabase * _Nullable)database updateId:(NSUUID * _Nonnull)updateId scopeKey:(NSString * _Nullable)scopeKey commitTime:(NSDate * _Nonnull)commitTime runtimeVersion:(NSString * _Nonnull)runtimeVersion keep:(BOOL)keep status:(enum EXUpdatesUpdateStatus)status isDevelopmentMode:(BOOL)isDevelopmentMode assetsFromManifest:(NSArray<EXUpdatesAsset *> * _Nullable)assetsFromManifest OBJC_DESIGNATED_INITIALIZER;
@end

/// Given a list of updates, implementations of this protocol should be able to choose one to launch.
SWIFT_PROTOCOL_NAMED("LauncherSelectionPolicy")
@protocol EXUpdatesLauncherSelectionPolicy
- (EXUpdatesUpdate * _Nullable)launchableUpdateFromUpdates:(NSArray<EXUpdatesUpdate *> * _Nonnull)updates filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
@end

/// A LauncherSelectionPolicy which chooses an update to launch based on the manifest
/// filters provided by the server. If multiple updates meet the criteria, the newest one (using
/// <code>commitTime</code> for ordering) is chosen, but the manifest filters are always taken into account
/// before the <code>commitTime</code>.
SWIFT_CLASS_NAMED("LauncherSelectionPolicyFilterAware")
@interface EXUpdatesLauncherSelectionPolicyFilterAware : NSObject <EXUpdatesLauncherSelectionPolicy>
- (nonnull instancetype)initWithRuntimeVersion:(NSString * _Nonnull)runtimeVersion OBJC_DESIGNATED_INITIALIZER;
- (EXUpdatesUpdate * _Nullable)launchableUpdateFromUpdates:(NSArray<EXUpdatesUpdate *> * _Nonnull)updates filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// A trivial LauncherSelectionPolicy that will choose a single predetermined update to launch.
SWIFT_CLASS_NAMED("LauncherSelectionPolicySingleUpdate")
@interface EXUpdatesLauncherSelectionPolicySingleUpdate : NSObject <EXUpdatesLauncherSelectionPolicy>
- (nonnull instancetype)initWithUpdateId:(NSUUID * _Nonnull)updateId OBJC_DESIGNATED_INITIALIZER;
- (EXUpdatesUpdate * _Nullable)launchableUpdateFromUpdates:(NSArray<EXUpdatesUpdate *> * _Nonnull)updates filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class EXUpdatesRollBackToEmbeddedUpdateDirective;
/// Implementations of this protocol should be able to determine whether to load (either fetch remotely
/// or copy from an embedded location) a new update, given information about the one currently
/// running.
SWIFT_PROTOCOL("_TtP9EXUpdates21LoaderSelectionPolicy_")
@protocol LoaderSelectionPolicy
- (BOOL)shouldLoadNewUpdate:(EXUpdatesUpdate * _Nullable)newUpdate withLaunchedUpdate:(EXUpdatesUpdate * _Nullable)launchedUpdate filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
/// Given a roll back to embedded directive, the embedded update before the directive is applied,
/// and the currently running update, decide whether the directive should be applied to the embedded
/// update and saved in the database (i.e. decide whether the combination of the directive’s commitTime
/// and the embedded update is “newer” than the currently running update, according to this class’s ordering).
- (BOOL)shouldLoadRollBackToEmbeddedDirective:(EXUpdatesRollBackToEmbeddedUpdateDirective * _Nonnull)directive withEmbeddedUpdate:(EXUpdatesUpdate * _Nonnull)embeddedUpdate launchedUpdate:(EXUpdatesUpdate * _Nullable)launchedUpdate filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
@end

/// A LoaderSelectionPolicy which decides whether or not to load an update or directive, taking filters into
/// account. Returns true (should load the update) if we don’t have an existing newer update that
/// matches the given manifest filters.
/// Uses <code>commitTime</code> to determine ordering of updates.
SWIFT_CLASS_NAMED("LoaderSelectionPolicyFilterAware")
@interface EXUpdatesLoaderSelectionPolicyFilterAware : NSObject <LoaderSelectionPolicy>
- (BOOL)shouldLoadNewUpdate:(EXUpdatesUpdate * _Nullable)newUpdate withLaunchedUpdate:(EXUpdatesUpdate * _Nullable)launchedUpdate filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
- (BOOL)shouldLoadRollBackToEmbeddedDirective:(EXUpdatesRollBackToEmbeddedUpdateDirective * _Nonnull)directive withEmbeddedUpdate:(EXUpdatesUpdate * _Nonnull)embeddedUpdate launchedUpdate:(EXUpdatesUpdate * _Nullable)launchedUpdate filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS_NAMED("UpdateDirective")
@interface EXUpdatesUpdateDirective : NSObject
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS("_TtC9EXUpdates32NoUpdateAvailableUpdateDirective")
@interface NoUpdateAvailableUpdateDirective : EXUpdatesUpdateDirective
@end

/// Given a list of updates, implementations of this protocol should choose which of those updates to
/// automatically delete from disk and which ones to keep.
SWIFT_PROTOCOL_NAMED("ReaperSelectionPolicy")
@protocol EXUpdatesReaperSelectionPolicy
- (NSArray<EXUpdatesUpdate *> * _Nonnull)updatesToDeleteWithLaunchedUpdate:(EXUpdatesUpdate * _Nonnull)launchedUpdate updates:(NSArray<EXUpdatesUpdate *> * _Nonnull)updates filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
@end

/// A ReaperSelectionPolicy which keeps a predefined maximum number of updates across all scopes,
/// and, once that number is surpassed, selects the updates least recently accessed (and then least
/// recently published) to delete. Ignores filters and scopes.
/// Uses the <code>lastAccessed</code> property to determine ordering of updates.
SWIFT_CLASS_NAMED("ReaperSelectionPolicyDevelopmentClient")
@interface EXUpdatesReaperSelectionPolicyDevelopmentClient : NSObject <EXUpdatesReaperSelectionPolicy>
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)initWithMaxUpdatesToKeep:(NSInteger)maxUpdatesToKeep OBJC_DESIGNATED_INITIALIZER;
- (NSArray<EXUpdatesUpdate *> * _Nonnull)updatesToDeleteWithLaunchedUpdate:(EXUpdatesUpdate * _Nonnull)launchedUpdate updates:(NSArray<EXUpdatesUpdate *> * _Nonnull)updates filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
@end

/// A ReaperSelectionPolicy which chooses which updates to delete taking into account manifest filters
/// originating from the server. If an older update is available, it will choose to keep one older
/// update in addition to the one currently running, preferring updates that match the same filters
/// if available.
/// Uses <code>commitTime</code> to determine ordering of updates.
/// Chooses only to delete updates who scope matches that of <code>launchedUpdate</code>.
SWIFT_CLASS_NAMED("ReaperSelectionPolicyFilterAware")
@interface EXUpdatesReaperSelectionPolicyFilterAware : NSObject <EXUpdatesReaperSelectionPolicy>
- (NSArray<EXUpdatesUpdate *> * _Nonnull)updatesToDeleteWithLaunchedUpdate:(EXUpdatesUpdate * _Nonnull)launchedUpdate updates:(NSArray<EXUpdatesUpdate *> * _Nonnull)updates filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// Subclass of AppLoader which handles downloading updates from a remote server.
SWIFT_CLASS("_TtC9EXUpdates15RemoteAppLoader")
@interface RemoteAppLoader : EXUpdatesAppLoader
- (void)downloadAsset:(EXUpdatesAsset * _Nonnull)asset extraHeaders:(NSDictionary<NSString *, id> * _Nonnull)extraHeaders;
@end

typedef SWIFT_ENUM_NAMED(NSInteger, EXUpdatesRemoteLoadStatus, "RemoteLoadStatus", closed) {
  EXUpdatesRemoteLoadStatusIdle = 0,
  EXUpdatesRemoteLoadStatusLoading = 1,
  EXUpdatesRemoteLoadStatusNewUpdateLoaded = 2,
};

SWIFT_CLASS_NAMED("RollBackToEmbeddedUpdateDirective")
@interface EXUpdatesRollBackToEmbeddedUpdateDirective : EXUpdatesUpdateDirective
@end

SWIFT_CLASS_NAMED("SelectionPolicies")
@interface EXUpdatesSelectionPolicies : NSObject
+ (BOOL)doesUpdate:(EXUpdatesUpdate * _Nonnull)update matchFilters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// Pluggable class whose essential responsibility is to determine an ordering of the updates stored
/// locally. Ordering updates is important in three separate cases, which map to the three methods
/// here.
/// The default/basic implementations of these methods use an ordering based on <code>commitTime</code> (with
/// allowances for EAS Update branches). This has implications for rollbacks (rolled back updates
/// must have a new <code>id</code> and <code>commitTime</code> in order to take effect), amongst other things, and so this
/// class was designed to be pluggable in order to allow different implementations to be swapped in
/// with relative ease, in situations with different tradeoffs.
/// The three methods are individually pluggable to allow for different behavior of specific parts of
/// the module in different situations. For example, in a development client, our policy for
/// retaining and deleting updates is different than in a release build, so we use a different
/// implementation of ReaperSelectionPolicy.
/// Importantly (and non-trivially), expo-updates must be able to make all these determinations
/// without talking to any server. This is because the embedded update can change at any time,
/// without warning, and without the opportunity to talk to the updates server - when a new build is
/// installed via the App Store/TestFlight/sideloading - and this class must be able to decide which
/// update to launch in that case.
SWIFT_CLASS_NAMED("SelectionPolicy")
@interface EXUpdatesSelectionPolicy : NSObject <EXUpdatesLauncherSelectionPolicy, LoaderSelectionPolicy, EXUpdatesReaperSelectionPolicy>
- (nonnull instancetype)initWithLauncherSelectionPolicy:(id <EXUpdatesLauncherSelectionPolicy> _Nonnull)launcherSelectionPolicy loaderSelectionPolicy:(id <LoaderSelectionPolicy> _Nonnull)loaderSelectionPolicy reaperSelectionPolicy:(id <EXUpdatesReaperSelectionPolicy> _Nonnull)reaperSelectionPolicy OBJC_DESIGNATED_INITIALIZER;
- (EXUpdatesUpdate * _Nullable)launchableUpdateFromUpdates:(NSArray<EXUpdatesUpdate *> * _Nonnull)updates filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
- (BOOL)shouldLoadNewUpdate:(EXUpdatesUpdate * _Nullable)newUpdate withLaunchedUpdate:(EXUpdatesUpdate * _Nullable)launchedUpdate filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
- (BOOL)shouldLoadRollBackToEmbeddedDirective:(EXUpdatesRollBackToEmbeddedUpdateDirective * _Nonnull)directive withEmbeddedUpdate:(EXUpdatesUpdate * _Nonnull)embeddedUpdate launchedUpdate:(EXUpdatesUpdate * _Nullable)launchedUpdate filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
- (NSArray<EXUpdatesUpdate *> * _Nonnull)updatesToDeleteWithLaunchedUpdate:(EXUpdatesUpdate * _Nonnull)launchedUpdate updates:(NSArray<EXUpdatesUpdate *> * _Nonnull)updates filters:(NSDictionary<NSString *, id> * _Nullable)filters SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Factory class to ease the construction of [SelectionPolicy] objects whose three methods all use
/// the same ordering policy.
SWIFT_CLASS_NAMED("SelectionPolicyFactory")
@interface EXUpdatesSelectionPolicyFactory : NSObject
+ (EXUpdatesSelectionPolicy * _Nonnull)filterAwarePolicyWithRuntimeVersion:(NSString * _Nonnull)runtimeVersion SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

/// Data class that represents an individual asset. The filename is autogenerated if not explicitly
/// set (it should be explicitly set when originating from a database query).
SWIFT_CLASS_NAMED("UpdateAsset")
@interface EXUpdatesAsset : NSObject
/// properties determined by asset source
@property (nonatomic, copy) NSString * _Nullable key;
@property (nonatomic, readonly, copy) NSString * _Nullable type;
@property (nonatomic, copy) NSURL * _Nullable url;
@property (nonatomic, copy) NSDictionary<NSString *, id> * _Nullable metadata;
@property (nonatomic, copy) NSString * _Nullable mainBundleDir;
@property (nonatomic, copy) NSString * _Nullable mainBundleFilename;
@property (nonatomic) BOOL isLaunchAsset;
@property (nonatomic, copy) NSDictionary<NSString *, id> * _Nullable extraRequestHeaders;
@property (nonatomic, copy) NSString * _Nullable expectedHash;
/// properties determined at runtime by updates implementation
@property (nonatomic, copy) NSDate * _Nullable downloadTime;
@property (nonatomic, copy) NSString * _Nullable contentHash;
@property (nonatomic, copy) NSDictionary<NSString *, id> * _Nullable headers;
/// properties determined by updates database
@property (nonatomic) NSInteger assetId;
- (nonnull instancetype)initWithKey:(NSString * _Nullable)key type:(NSString * _Nullable)type OBJC_DESIGNATED_INITIALIZER;
@property (nonatomic, copy) NSString * _Nonnull filename;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Download status that indicates whether or under what conditions an
/// update is able to be launched.
/// It’s important that the integer value of each status stays constant across
/// all versions of this library since they are stored in SQLite on user devices.
typedef SWIFT_ENUM_NAMED(NSInteger, EXUpdatesUpdateStatus, "UpdateStatus", closed) {
  EXUpdatesUpdateStatusStatus0_Unused = 0,
/// The update has been fully downloaded and is ready to launch.
  EXUpdatesUpdateStatusStatusReady = 1,
  EXUpdatesUpdateStatusStatus2_Unused = 2,
/// The update manifest has been download from the server but not all
/// assets have finished downloading successfully.
  EXUpdatesUpdateStatusStatusPending = 3,
  EXUpdatesUpdateStatusStatus4_Unused = 4,
/// The update has been partially loaded (copied) from its location
/// embedded in the app bundle, but not all assets have been copied
/// successfully. The update may be able to be launched directly from
/// its embedded location unless a new binary version with a new
/// embedded update has been installed.
  EXUpdatesUpdateStatusStatusEmbedded = 5,
/// The update manifest has been downloaded and indicates that the
/// update is being served from a developer tool. It can be launched by a
/// host application that can run a development bundle.
  EXUpdatesUpdateStatusStatusDevelopment = 6,
};

/// Holds global, immutable configuration values for updates, as well as doing some rudimentary
/// validation.
/// In most apps, these configuration values are baked into the build, and this class functions as a
/// utility for reading and memoizing the values.
/// In development clients (including Expo Go) where this configuration is intended to be dynamic at
/// runtime and updates from multiple scopes can potentially be opened, multiple instances of this
/// class may be created over the lifetime of the app, but only one should be active at a time.
SWIFT_CLASS_NAMED("UpdatesConfig")
@interface EXUpdatesConfig : NSObject
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull PlistName;)
+ (NSString * _Nonnull)PlistName SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigEnabledKey;)
+ (NSString * _Nonnull)EXUpdatesConfigEnabledKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigScopeKeyKey;)
+ (NSString * _Nonnull)EXUpdatesConfigScopeKeyKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigUpdateUrlKey;)
+ (NSString * _Nonnull)EXUpdatesConfigUpdateUrlKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigRequestHeadersKey;)
+ (NSString * _Nonnull)EXUpdatesConfigRequestHeadersKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigLaunchWaitMsKey;)
+ (NSString * _Nonnull)EXUpdatesConfigLaunchWaitMsKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigCheckOnLaunchKey;)
+ (NSString * _Nonnull)EXUpdatesConfigCheckOnLaunchKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigRuntimeVersionKey;)
+ (NSString * _Nonnull)EXUpdatesConfigRuntimeVersionKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigHasEmbeddedUpdateKey;)
+ (NSString * _Nonnull)EXUpdatesConfigHasEmbeddedUpdateKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigCodeSigningCertificateKey;)
+ (NSString * _Nonnull)EXUpdatesConfigCodeSigningCertificateKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigCodeSigningMetadataKey;)
+ (NSString * _Nonnull)EXUpdatesConfigCodeSigningMetadataKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigCodeSigningIncludeManifestResponseCertificateChainKey;)
+ (NSString * _Nonnull)EXUpdatesConfigCodeSigningIncludeManifestResponseCertificateChainKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigCodeSigningAllowUnsignedManifestsKey;)
+ (NSString * _Nonnull)EXUpdatesConfigCodeSigningAllowUnsignedManifestsKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigEnableExpoUpdatesProtocolV0CompatibilityModeKey;)
+ (NSString * _Nonnull)EXUpdatesConfigEnableExpoUpdatesProtocolV0CompatibilityModeKey SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigDisableAntiBrickingMeasures;)
+ (NSString * _Nonnull)EXUpdatesConfigDisableAntiBrickingMeasures SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigCheckOnLaunchValueAlways;)
+ (NSString * _Nonnull)EXUpdatesConfigCheckOnLaunchValueAlways SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigCheckOnLaunchValueWifiOnly;)
+ (NSString * _Nonnull)EXUpdatesConfigCheckOnLaunchValueWifiOnly SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigCheckOnLaunchValueErrorRecoveryOnly;)
+ (NSString * _Nonnull)EXUpdatesConfigCheckOnLaunchValueErrorRecoveryOnly SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigCheckOnLaunchValueNever;)
+ (NSString * _Nonnull)EXUpdatesConfigCheckOnLaunchValueNever SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, copy) NSString * _Nonnull EXUpdatesConfigRuntimeVersionReadFingerprintFileSentinel;)
+ (NSString * _Nonnull)EXUpdatesConfigRuntimeVersionReadFingerprintFileSentinel SWIFT_WARN_UNUSED_RESULT;
@property (nonatomic, readonly, copy) NSString * _Nonnull scopeKey;
@property (nonatomic, readonly, copy) NSURL * _Nonnull updateUrl;
@property (nonatomic, readonly, copy) NSDictionary<NSString *, NSString *> * _Nonnull requestHeaders;
@property (nonatomic, readonly) NSInteger launchWaitMs;
@property (nonatomic, readonly) enum EXUpdatesCheckAutomaticallyConfig checkOnLaunch;
@property (nonatomic, readonly, strong) EXUpdatesCodeSigningConfiguration * _Nullable codeSigningConfiguration;
@property (nonatomic, readonly) BOOL enableExpoUpdatesProtocolV0CompatibilityMode;
@property (nonatomic, readonly, copy) NSString * _Nonnull runtimeVersion;
@property (nonatomic, readonly) BOOL hasEmbeddedUpdate;
@property (nonatomic, readonly) BOOL disableAntiBrickingMeasures;
+ (EXUpdatesConfig * _Nullable)configWithExpoPlistWithMergingOtherDictionary:(NSDictionary<NSString *, id> * _Nullable)mergingOtherDictionary error:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
+ (EXUpdatesConfig * _Nullable)configFromDictionary:(NSDictionary<NSString *, id> * _Nonnull)config error:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
+ (NSString * _Nonnull)normalizedURLOriginWithUrl:(NSURL * _Nonnull)url SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// SQLite database that keeps track of updates currently loaded/loading to disk, including the
/// update manifest and metadata, status, and the individual assets (including bundles/bytecode) that
/// comprise the update. (Assets themselves are stored on the device’s file system, and a relative
/// path is kept in SQLite.)
/// SQLite allows a many-to-many relationship between updates and assets, which means we can keep
/// only one copy of each asset on disk at a time while also being able to clear unused assets with
/// relative ease (see UpdatesReaper).
/// Occasionally it’s necessary to add migrations when the data structures for updates or assets must
/// change. Extra care must be taken here, since these migrations will happen on users’ devices for
/// apps we do not control. See
/// https://github.com/expo/expo/blob/main/packages/expo-updates/guides/migrations.md for step by
/// step instructions.
/// UpdatesDatabase provides a serial queue on which all database operations must be run (methods
/// in this class will assert). This is primarily for control over what high-level operations
/// involving the database can occur simultaneously - e.g. we don’t want to be trying to download a
/// new update at the same time UpdatesReaper is running.
/// The <code>scopeKey</code> field in various methods here is only relevant in environments such as Expo Go in
/// which updates from multiple scopes can be launched.
SWIFT_CLASS_NAMED("UpdatesDatabase")
@interface EXUpdatesDatabase : NSObject
@property (nonatomic, readonly, strong) dispatch_queue_t _Nonnull databaseQueue;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
- (BOOL)openDatabaseInDirectory:(NSURL * _Nonnull)directory error:(NSError * _Nullable * _Nullable)error;
- (void)closeDatabase;
- (NSArray * _Nullable)executeForObjCWithSql:(NSString * _Nonnull)sql withArgs:(NSArray * _Nullable)args error:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
- (BOOL)addUpdate:(EXUpdatesUpdate * _Nonnull)update error:(NSError * _Nullable * _Nullable)error;
- (BOOL)addNewAssets:(NSArray<EXUpdatesAsset *> * _Nonnull)assets toUpdateWithId:(NSUUID * _Nonnull)updateId error:(NSError * _Nullable * _Nullable)error;
- (BOOL)updateAsset:(EXUpdatesAsset * _Nonnull)asset error:(NSError * _Nullable * _Nullable)error;
- (BOOL)mergeAsset:(EXUpdatesAsset * _Nonnull)asset withExistingEntry:(EXUpdatesAsset * _Nonnull)existingAsset error:(NSError * _Nullable * _Nullable)error;
- (BOOL)markUpdateFinished:(EXUpdatesUpdate * _Nonnull)update error:(NSError * _Nullable * _Nullable)error;
- (BOOL)markUpdateAccessed:(EXUpdatesUpdate * _Nonnull)update error:(NSError * _Nullable * _Nullable)error;
- (BOOL)incrementSuccessfulLaunchCountForUpdate:(EXUpdatesUpdate * _Nonnull)update error:(NSError * _Nullable * _Nullable)error;
- (BOOL)incrementFailedLaunchCountForUpdate:(EXUpdatesUpdate * _Nonnull)update error:(NSError * _Nullable * _Nullable)error;
- (BOOL)setScopeKey:(NSString * _Nonnull)scopeKey onUpdate:(EXUpdatesUpdate * _Nonnull)update error:(NSError * _Nullable * _Nullable)error;
- (BOOL)setUpdateCommitTime:(NSDate * _Nonnull)commitTime onUpdate:(EXUpdatesUpdate * _Nonnull)update error:(NSError * _Nullable * _Nullable)error;
- (BOOL)markMissingAssets:(NSArray<EXUpdatesAsset *> * _Nonnull)assets error:(NSError * _Nullable * _Nullable)error;
- (BOOL)deleteUpdates:(NSArray<EXUpdatesUpdate *> * _Nonnull)updates error:(NSError * _Nullable * _Nullable)error;
- (NSArray<EXUpdatesAsset *> * _Nullable)deleteUnusedAssetsAndReturnError:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
- (NSArray<EXUpdatesUpdate *> * _Nullable)allUpdatesWithConfig:(EXUpdatesConfig * _Nonnull)config error:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
- (NSArray<EXUpdatesUpdate *> * _Nullable)allUpdatesWithStatus:(enum EXUpdatesUpdateStatus)status config:(EXUpdatesConfig * _Nonnull)config error:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
- (NSArray<NSUUID *> * _Nullable)recentUpdateIdsWithFailedLaunchAndReturnError:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
- (NSArray<EXUpdatesUpdate *> * _Nullable)launchableUpdatesWithConfig:(EXUpdatesConfig * _Nonnull)config error:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
- (NSArray<EXUpdatesAsset *> * _Nullable)allAssetsAndReturnError:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
- (NSArray<EXUpdatesAsset *> * _Nullable)assetsWithUpdateId:(NSUUID * _Nonnull)updateId error:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
- (BOOL)setServerDefinedHeaders:(NSDictionary<NSString *, id> * _Nonnull)serverDefinedHeaders withScopeKey:(NSString * _Nonnull)scopeKey error:(NSError * _Nullable * _Nullable)error;
- (BOOL)setManifestFilters:(NSDictionary<NSString *, id> * _Nonnull)manifestFilters withScopeKey:(NSString * _Nonnull)scopeKey error:(NSError * _Nullable * _Nullable)error;
- (BOOL)setStaticBuildData:(NSDictionary<NSString *, id> * _Nonnull)staticBuildData withScopeKey:(NSString * _Nonnull)scopeKey error:(NSError * _Nullable * _Nullable)error;
- (BOOL)setExtraParamWithKey:(NSString * _Nonnull)key value:(NSString * _Nullable)value withScopeKey:(NSString * _Nonnull)scopeKey error:(NSError * _Nullable * _Nullable)error;
@end

SWIFT_CLASS_NAMED("UpdatesReaper")
@interface EXUpdatesReaper : NSObject
/// Safely clears old, unused assets and updates from the filesystem and database.
/// Should be run when no other updates-related events are occurring (e.g. update download).
+ (void)reapUnusedUpdatesWithConfig:(EXUpdatesConfig * _Nonnull)config database:(EXUpdatesDatabase * _Nonnull)database directory:(NSURL * _Nonnull)directory selectionPolicy:(EXUpdatesSelectionPolicy * _Nonnull)selectionPolicy launchedUpdate:(EXUpdatesUpdate * _Nonnull)launchedUpdate;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

SWIFT_CLASS_NAMED("UpdatesUtils")
@interface EXUpdatesUtils : NSObject
+ (NSURL * _Nonnull)updatesApplicationDocumentsDirectory SWIFT_WARN_UNUSED_RESULT;
+ (NSURL * _Nullable)initializeUpdatesDirectoryAndReturnError:(NSError * _Nullable * _Nullable)error SWIFT_WARN_UNUSED_RESULT;
+ (BOOL)isNativeDebuggingEnabled SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init OBJC_DESIGNATED_INITIALIZER;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
