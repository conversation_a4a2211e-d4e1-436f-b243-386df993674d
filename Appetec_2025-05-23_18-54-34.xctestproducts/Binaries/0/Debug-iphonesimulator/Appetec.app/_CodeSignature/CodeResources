<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key><EMAIL></key>
		<data>
		QOUhZVsHxFXEinUu8lXDTFXYb8U=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		WmHiit4f3hCa4ndYdL5y7EFWtvU=
		</data>
		<key>Appetec.debug.dylib</key>
		<data>
		ng+WXnQKJLU+UM8pCQWrK3YRhX4=
		</data>
		<key>Assets.car</key>
		<data>
		BM0FQYY16xFp+/K3IcBin1lVpYM=
		</data>
		<key>EXConstants.bundle/Info.plist</key>
		<data>
		QlAqmC2hGFN4VrSWhl+FHC5s9lU=
		</data>
		<key>EXConstants.bundle/app.config</key>
		<data>
		y9zw6h7uep1Z0vdsJak/oPdzs5Y=
		</data>
		<key>EXUpdates.bundle/Info.plist</key>
		<data>
		Esji/Wf1YzSdG1uSl7HBpHtT4g8=
		</data>
		<key>Expo.plist</key>
		<data>
		PjVk04iznKvE68RMsfpKeul8fyk=
		</data>
		<key>ExpoApplication_privacy.bundle/Info.plist</key>
		<data>
		R4rMghIJOf66VIylQNwseSSBxnc=
		</data>
		<key>ExpoApplication_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		mUc2YHiDtobIhFXi+Mvm12TXeb8=
		</data>
		<key>ExpoConstants_privacy.bundle/Info.plist</key>
		<data>
		DaN3g459YNwLHOOyzCdCeg1gxhY=
		</data>
		<key>ExpoConstants_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		dHAsEQehwJCS8hMOpBoz7emiNj8=
		</data>
		<key>ExpoDevice_privacy.bundle/Info.plist</key>
		<data>
		sdBJ0sUAT8Wk+XrAqYD64KOu1pc=
		</data>
		<key>ExpoDevice_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		hWEgzzi+YPgmddeTqWfAi6jGQ0E=
		</data>
		<key>ExpoFileSystem_privacy.bundle/Info.plist</key>
		<data>
		jZDwNabMJnFYjWCWHOvxq2W2ot8=
		</data>
		<key>ExpoFileSystem_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		UieOpg4b1PxYR6jA3/cs9mU9rdo=
		</data>
		<key>ExpoNotifications_privacy.bundle/Info.plist</key>
		<data>
		+Rp6zZy6BNbRP0bcHhYewAokcNk=
		</data>
		<key>ExpoNotifications_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		dHAsEQehwJCS8hMOpBoz7emiNj8=
		</data>
		<key>ExpoSystemUI_privacy.bundle/Info.plist</key>
		<data>
		mxr2qWlDeaqVmfBewvLJ0b/8X5M=
		</data>
		<key>ExpoSystemUI_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		dHAsEQehwJCS8hMOpBoz7emiNj8=
		</data>
		<key>ExpoTaskManager_privacy.bundle/Info.plist</key>
		<data>
		LNDnf0uT18hrCKPRoD1jBnyhu38=
		</data>
		<key>ExpoTaskManager_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		dHAsEQehwJCS8hMOpBoz7emiNj8=
		</data>
		<key>Info.plist</key>
		<data>
		35hwZYwjsWlv3DlThqvAW0nHLMg=
		</data>
		<key>LottiePrivacyInfo.bundle/Info.plist</key>
		<data>
		ci+4yVRL8Xh8pwVDv7Hv00EAkaE=
		</data>
		<key>LottiePrivacyInfo.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		9slhmTZYKDmos4efk+3YOXYOQXo=
		</data>
		<key>Lottie_React_Native_Privacy.bundle/Info.plist</key>
		<data>
		rCARrVOAoF5ePLKqQZvmtt6kYUQ=
		</data>
		<key>Lottie_React_Native_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ucg9pita0v8d353x3NuGfxweQYU=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		QWVPQQrLs8XwFZWrDE5vARWvUdA=
		</data>
		<key>RCT-Folly_privacy.bundle/Info.plist</key>
		<data>
		1PfSDxH9FYq8zRogGet36JexVw8=
		</data>
		<key>RNCAsyncStorage_resources.bundle/Info.plist</key>
		<data>
		BzB/1SeIo9H5JnlRxY5P1CqKgC4=
		</data>
		<key>RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		sPDfyCwXVFwSP8cGRWRskpblWgM=
		</data>
		<key>RNDeviceInfoPrivacyInfo.bundle/Info.plist</key>
		<data>
		3MXdslBYIL1eNvoiCGInMzFtYm8=
		</data>
		<key>RNDeviceInfoPrivacyInfo.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		m8aNKGgCLWHtBQWong4x8zsNang=
		</data>
		<key>ReachabilitySwift.bundle/Info.plist</key>
		<data>
		81aQMLhrMqdWhPvrnbtA0+Htv+A=
		</data>
		<key>ReachabilitySwift.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		0RESd+++ZxZWQhIEMSOOvP7phYs=
		</data>
		<key>React-Core_privacy.bundle/Info.plist</key>
		<data>
		0GVMVzNT8MDo0coCOK2oOh4eLa0=
		</data>
		<key>React-Core_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ZahcOiTSEcJJdvNh0xqgAKPqbMs=
		</data>
		<key>React-cxxreact_privacy.bundle/Info.plist</key>
		<data>
		1ljPA2GO/E96goyi/+cWdcdc4eQ=
		</data>
		<key>React-cxxreact_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		dxJQFdQ77efnBkB0VBZmuIamJ4g=
		</data>
		<key>SplashScreen.storyboardc/EXPO-VIEWCONTROLLER-1-view-EXPO-ContainerView.nib</key>
		<data>
		cV9/0VoVgVUchTDFJ15aUUeA1nw=
		</data>
		<key>SplashScreen.storyboardc/Info.plist</key>
		<data>
		E68oTK3pecVBbvVw/Td2doSlDiA=
		</data>
		<key>SplashScreen.storyboardc/SplashScreenViewController.nib</key>
		<data>
		XO9GpHETPa/KEeOkIqhZoQ6OIvU=
		</data>
		<key>__preview.dylib</key>
		<data>
		7EPbEhjKMDaR5XWNTMwiqFTxOLM=
		</data>
		<key>boost_privacy.bundle/Info.plist</key>
		<data>
		rRtTxwou1gbezEAUJa2L1YnN4Q8=
		</data>
		<key>glog_privacy.bundle/Info.plist</key>
		<data>
		mvvSXKorpyCTgGpjiTkgWYWDQ1Y=
		</data>
		<key>sound1.wav</key>
		<data>
		GQmCk3bPxX6jPV2W+3+J53Ztqt8=
		</data>
		<key>sound2.wav</key>
		<data>
		p7Mp7+UNBLxb8qp1Mt9eupxfOjc=
		</data>
		<key>sound3.wav</key>
		<data>
		/g3zdRPgZegSckHvWb/Gbf4wD/s=
		</data>
		<key>sound4.wav</key>
		<data>
		xd1/ur/RigEItRn3xz/JsK/Eigw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			8ngimekf0qKs6KZyn2hSEtOTGI//57eY7ZzoF01/NY8=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WdmMpXgVdfZCkZ09hvbQcKyOT21Pmkh/2vm4cirCvvY=
			</data>
		</dict>
		<key>Appetec.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			0IPAXRwqd0ayUM3PfRr7NNbq+SHP3GuLFUmEccqD4FM=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			OaQimI95Ni70F4pPrnEXRY4vZD6ITP7CbaCr0O+ZcU0=
			</data>
		</dict>
		<key>EXConstants.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			MhYF21JjCGJRnuN6I8VijxqXiyB0ddkASy5tRZhnPHc=
			</data>
		</dict>
		<key>EXConstants.bundle/app.config</key>
		<dict>
			<key>hash2</key>
			<data>
			8Iirx/N2ql03XoTRjOtkTun1or5VrtcrresgX9eVHtU=
			</data>
		</dict>
		<key>EXUpdates.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			pJw6D0O1aarJTI0iC1jWKOsHzNtvvlKbyPkIFcoh0Ks=
			</data>
		</dict>
		<key>Expo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			vIXtu2Z6xBkeaghCnrR4kLyj3msI7U3cWSM4C+dy15M=
			</data>
		</dict>
		<key>ExpoApplication_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			uKUOx4wqLRiMEfZf4uE/xPEkrv6DPsdn6F+FYGqfT7c=
			</data>
		</dict>
		<key>ExpoApplication_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			tIdj/9KcutgVElrlbhBzJz5BiuCGED/H3/fvvsFnWqo=
			</data>
		</dict>
		<key>ExpoConstants_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			4x0n/YRv4/cyVMQ+9ki68pCrX87xmLHF20vcpo0K9B0=
			</data>
		</dict>
		<key>ExpoConstants_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Ky2O23HVHFsfGs5M2yipS68i/d6bvy4r/BfRh/97X0c=
			</data>
		</dict>
		<key>ExpoDevice_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			AGEgaDzW5m5usqy9hW9FxcEGiZRlNi1hQGDf8DXjpM0=
			</data>
		</dict>
		<key>ExpoDevice_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			2JioNW3Ie3zSsXkh1opGNtQkBns6dcg7eTX9eXcZycs=
			</data>
		</dict>
		<key>ExpoFileSystem_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			7XL7qXMp70JkHmETeNZ7tA3ucyklHOBierDEDhA4NZY=
			</data>
		</dict>
		<key>ExpoFileSystem_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			M7DgdPJz9YimS6VjKQnGqM8fFtuOTNhMaXi9Ii39Zb0=
			</data>
		</dict>
		<key>ExpoNotifications_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			ORa91YV3MKWHZgubhhZB3MHyM0sXyR7S1n/dsfh6/EU=
			</data>
		</dict>
		<key>ExpoNotifications_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Ky2O23HVHFsfGs5M2yipS68i/d6bvy4r/BfRh/97X0c=
			</data>
		</dict>
		<key>ExpoSystemUI_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			WySPyRHVpmHJwa9yQmM91hPHTjLwmpKXhaoxIpPLuX4=
			</data>
		</dict>
		<key>ExpoSystemUI_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Ky2O23HVHFsfGs5M2yipS68i/d6bvy4r/BfRh/97X0c=
			</data>
		</dict>
		<key>ExpoTaskManager_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			BdynLF+HpRe94VeEXgrJeNtG1wLhxqUI6Fl5JeRbfOw=
			</data>
		</dict>
		<key>ExpoTaskManager_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Ky2O23HVHFsfGs5M2yipS68i/d6bvy4r/BfRh/97X0c=
			</data>
		</dict>
		<key>LottiePrivacyInfo.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			H0iTw0AqVWEdh58TelU8934nh/tbM/+vLb/GKf+Zy9E=
			</data>
		</dict>
		<key>LottiePrivacyInfo.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ENpn8heCTwGSiOwyirq8KQqwOZxS6b53ok1JQzkTfaI=
			</data>
		</dict>
		<key>Lottie_React_Native_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			JX+d9BKVZMon3OJM54dgd9876uMeHFSVtRFdLmy+ZYk=
			</data>
		</dict>
		<key>Lottie_React_Native_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Uh6274Qwdz5cAQ4YOP6d2PpdYre3bRzqjX2NqtyxROI=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			0iT0B29OMhTbiXYnweoynu+q6Im8gta4P/eeuquI8zU=
			</data>
		</dict>
		<key>RCT-Folly_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			/LXR7lQZl18hoiIYt1/1fN9RupMwaBaVyc6QQxkaMyk=
			</data>
		</dict>
		<key>RNCAsyncStorage_resources.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			x89nYfozkbAuIhSbxbfLAlkCTQ51hxVWW44VHpO+oUE=
			</data>
		</dict>
		<key>RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			CxKH0OaGz4Z9ONl8yl3GlLMCnyfE/kJw3UvS/VRYovA=
			</data>
		</dict>
		<key>RNDeviceInfoPrivacyInfo.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			WqfqDB/o73SWh1cO1Tj5M12JsikXBL08Cy38msQJscY=
			</data>
		</dict>
		<key>RNDeviceInfoPrivacyInfo.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			4Ye67kwWytlqe/PE3iAZKFQmf1/L4XhtOgUgWAH3aeY=
			</data>
		</dict>
		<key>ReachabilitySwift.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			u4vJFZDVrIWwbqOdkOscVefffdV6+Rk7MWxoE3rQrRs=
			</data>
		</dict>
		<key>ReachabilitySwift.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			pwgfFQbJDo5nMQDlwWHnZbi3piREbiC9S9bvrKgICLg=
			</data>
		</dict>
		<key>React-Core_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Mg0pz0P0SFEWqWFEWYp7NT8jqdmAjgAXci9Wlbrde8E=
			</data>
		</dict>
		<key>React-Core_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			fAiWnkWWIabs7AQ8zbkBJlHxhYEZRfdVF8Yj2uLhFic=
			</data>
		</dict>
		<key>React-cxxreact_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			WOEXvLE7ZMGorN2+h1nyuqoY6qz05/kCdcu+rJxYIxM=
			</data>
		</dict>
		<key>React-cxxreact_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BYfRVcVqb08GR+vGtqC9AmYVzWEO6PIJqXhrealq0zU=
			</data>
		</dict>
		<key>SplashScreen.storyboardc/EXPO-VIEWCONTROLLER-1-view-EXPO-ContainerView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			k4ydNrb+KUt3uZwdnO59fXEOp5AFUFkGrUv/4ujN1CM=
			</data>
		</dict>
		<key>SplashScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			dNvO7WzwpeXGmDR5MyjJeD7Ksd5ILUlU4lfKITK3Q68=
			</data>
		</dict>
		<key>SplashScreen.storyboardc/SplashScreenViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			nBo0wSHSJHlAjPDqrLNJFUjO0WZVeZuuO19/I4AxS6g=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			EvzJC0CVHF19/W5xW+XYSYH2R0KFeGzmSevjTHshdyA=
			</data>
		</dict>
		<key>boost_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			XLJ4yqQs8+3dVKFzC4SFLmDb+zU0ldP3KV5WyMhvDts=
			</data>
		</dict>
		<key>glog_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			kVy5iyruQU9D0z3rYHO267t6BcDxGZXv7ZUR9wLFYtc=
			</data>
		</dict>
		<key>sound1.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			LvgRs8NGb78nonadCAEsT3L2mdYRy72bqJrxNsbhB4Y=
			</data>
		</dict>
		<key>sound2.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			9xwpm6BvvWXfdnopCTViymbTkNSGHgQQbTk+aVIbMkY=
			</data>
		</dict>
		<key>sound3.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			VRyemjItKe+yRYK3Rl2MVgUzi9Jj1chOuTwUjPBkjUg=
			</data>
		</dict>
		<key>sound4.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			CEt4Rj4w2uSqxe2/npyTze5ezkpKaINS5qK2aG+bH0o=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
