<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key><EMAIL></key>
		<data>
		/0Jrt5c/q53MapF25Zbxs4L9yl4=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		8UGCTAzmM4YhMaUncnlrYu2uPfo=
		</data>
		<key>Appetec.debug.dylib</key>
		<data>
		YLFRG018CnnAKoP8xA/5hQnbDZg=
		</data>
		<key>Assets.car</key>
		<data>
		uOdMKFE0kjyr8/5zbgHD3ckudp8=
		</data>
		<key>EXConstants.bundle/Info.plist</key>
		<data>
		7Wi2Gv2opEqN+l+zhjHidp4ZjpU=
		</data>
		<key>EXConstants.bundle/app.config</key>
		<data>
		y9zw6h7uep1Z0vdsJak/oPdzs5Y=
		</data>
		<key>EXUpdates.bundle/Info.plist</key>
		<data>
		Q3sEgQhKTeMQyLDg1uyEiJalKCE=
		</data>
		<key>Expo.plist</key>
		<data>
		PjVk04iznKvE68RMsfpKeul8fyk=
		</data>
		<key>ExpoApplication_privacy.bundle/Info.plist</key>
		<data>
		yAeFlmzwy8g6f02CbxfNv3585Fo=
		</data>
		<key>ExpoApplication_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		mUc2YHiDtobIhFXi+Mvm12TXeb8=
		</data>
		<key>ExpoConstants_privacy.bundle/Info.plist</key>
		<data>
		nk1c9mVB0mV1QCRLsfv9p46IEfo=
		</data>
		<key>ExpoConstants_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		dHAsEQehwJCS8hMOpBoz7emiNj8=
		</data>
		<key>ExpoDevice_privacy.bundle/Info.plist</key>
		<data>
		Faj2LJkDFSAHu9Rwlw0y9aQOac8=
		</data>
		<key>ExpoDevice_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		hWEgzzi+YPgmddeTqWfAi6jGQ0E=
		</data>
		<key>ExpoFileSystem_privacy.bundle/Info.plist</key>
		<data>
		X4JhKlHJbjdGTzOFt17hqsmP2vw=
		</data>
		<key>ExpoFileSystem_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		UieOpg4b1PxYR6jA3/cs9mU9rdo=
		</data>
		<key>ExpoNotifications_privacy.bundle/Info.plist</key>
		<data>
		+dyguojLOGuz47QcbshE7UOQ0FY=
		</data>
		<key>ExpoNotifications_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		dHAsEQehwJCS8hMOpBoz7emiNj8=
		</data>
		<key>ExpoSystemUI_privacy.bundle/Info.plist</key>
		<data>
		SI5rI4+5sO114tob3qppyLnSdL8=
		</data>
		<key>ExpoSystemUI_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		dHAsEQehwJCS8hMOpBoz7emiNj8=
		</data>
		<key>ExpoTaskManager_privacy.bundle/Info.plist</key>
		<data>
		BgqoPdWm6KUBLyHqmWx8l5azzjE=
		</data>
		<key>ExpoTaskManager_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		dHAsEQehwJCS8hMOpBoz7emiNj8=
		</data>
		<key>Info.plist</key>
		<data>
		cstjngYZIv+fY6wmk2ygz0Orn/M=
		</data>
		<key>LottiePrivacyInfo.bundle/Info.plist</key>
		<data>
		QnJ39gFg6XH6CiSxJPxqL+/pRyA=
		</data>
		<key>LottiePrivacyInfo.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		9slhmTZYKDmos4efk+3YOXYOQXo=
		</data>
		<key>Lottie_React_Native_Privacy.bundle/Info.plist</key>
		<data>
		5hW4ZXnBxZLNHSFPzEwzlbua6UY=
		</data>
		<key>Lottie_React_Native_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ucg9pita0v8d353x3NuGfxweQYU=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		QWVPQQrLs8XwFZWrDE5vARWvUdA=
		</data>
		<key>RCT-Folly_privacy.bundle/Info.plist</key>
		<data>
		OVbq5Y7FAMfTbqjb2uZK57ZfW1E=
		</data>
		<key>RNCAsyncStorage_resources.bundle/Info.plist</key>
		<data>
		RIpDXLCsMkxedX13aKPmhvNINPc=
		</data>
		<key>RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		sPDfyCwXVFwSP8cGRWRskpblWgM=
		</data>
		<key>RNDeviceInfoPrivacyInfo.bundle/Info.plist</key>
		<data>
		GL8QQFbqMwItLhmNxo7P94v3M4Q=
		</data>
		<key>RNDeviceInfoPrivacyInfo.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		m8aNKGgCLWHtBQWong4x8zsNang=
		</data>
		<key>ReachabilitySwift.bundle/Info.plist</key>
		<data>
		QmW7qjhEVucOMWOun9IlPupgHvM=
		</data>
		<key>ReachabilitySwift.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		0RESd+++ZxZWQhIEMSOOvP7phYs=
		</data>
		<key>React-Core_privacy.bundle/Info.plist</key>
		<data>
		SK38idC5JTUEzRzbWOLvzf4yLyw=
		</data>
		<key>React-Core_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		ZahcOiTSEcJJdvNh0xqgAKPqbMs=
		</data>
		<key>React-cxxreact_privacy.bundle/Info.plist</key>
		<data>
		Ub3+DeeCtz25yF3DmecryLX70u4=
		</data>
		<key>React-cxxreact_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		dxJQFdQ77efnBkB0VBZmuIamJ4g=
		</data>
		<key>SplashScreen.storyboardc/EXPO-VIEWCONTROLLER-1-view-EXPO-ContainerView.nib</key>
		<data>
		cV9/0VoVgVUchTDFJ15aUUeA1nw=
		</data>
		<key>SplashScreen.storyboardc/Info.plist</key>
		<data>
		E68oTK3pecVBbvVw/Td2doSlDiA=
		</data>
		<key>SplashScreen.storyboardc/SplashScreenViewController.nib</key>
		<data>
		XO9GpHETPa/KEeOkIqhZoQ6OIvU=
		</data>
		<key>__preview.dylib</key>
		<data>
		+Ssm+T3BDmSO/ZPs2YN4XbBdfKk=
		</data>
		<key>boost_privacy.bundle/Info.plist</key>
		<data>
		Ndws3SdLLYloIeRNUZ/M2ny+4QM=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		i5dpKmFYc9CYc9TNYIYGBrn4aDE=
		</data>
		<key>glog_privacy.bundle/Info.plist</key>
		<data>
		f3fexk8OzxheLlEONBpuApTTIY8=
		</data>
		<key>ip.txt</key>
		<data>
		9UoQHISGyQgytfwdBU7jbf2uGgY=
		</data>
		<key>sound1.wav</key>
		<data>
		GQmCk3bPxX6jPV2W+3+J53Ztqt8=
		</data>
		<key>sound2.wav</key>
		<data>
		p7Mp7+UNBLxb8qp1Mt9eupxfOjc=
		</data>
		<key>sound3.wav</key>
		<data>
		/g3zdRPgZegSckHvWb/Gbf4wD/s=
		</data>
		<key>sound4.wav</key>
		<data>
		xd1/ur/RigEItRn3xz/JsK/Eigw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			OGU36DNwKn7laNYfdBqyL5YfSCCmxLHxbZQ0ZXDhOkE=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			aRcLKPD7uT9AU9+xHrqC0lEWCAWhQlNHy0E44eOG1n8=
			</data>
		</dict>
		<key>Appetec.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			qR1To4b1edf1xKOUT2hzaMDPPjYSlSBJEt7sNc/GD4Y=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			E1xvFEF4V3R4T5QgKdEBZEg/szXbsWjEAGoGQ0r1fR4=
			</data>
		</dict>
		<key>EXConstants.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			vZJM1Zm3or1Ik/bxe3206WsE/4qI43O1okY2VhkEFjE=
			</data>
		</dict>
		<key>EXConstants.bundle/app.config</key>
		<dict>
			<key>hash2</key>
			<data>
			8Iirx/N2ql03XoTRjOtkTun1or5VrtcrresgX9eVHtU=
			</data>
		</dict>
		<key>EXUpdates.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			5MfjbgAwLKs+aUezExYSoieL9AwGeZA1Fk65SJg8cDE=
			</data>
		</dict>
		<key>Expo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			vIXtu2Z6xBkeaghCnrR4kLyj3msI7U3cWSM4C+dy15M=
			</data>
		</dict>
		<key>ExpoApplication_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			wWCUmSDZWPkbJL9c5wl/qSEqFUw+RoODgS1zGqFX79o=
			</data>
		</dict>
		<key>ExpoApplication_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			tIdj/9KcutgVElrlbhBzJz5BiuCGED/H3/fvvsFnWqo=
			</data>
		</dict>
		<key>ExpoConstants_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			qnB9fvRH1nkV4IWz9lJvHBTv7zW38qwYRE7BQ1aDRMI=
			</data>
		</dict>
		<key>ExpoConstants_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Ky2O23HVHFsfGs5M2yipS68i/d6bvy4r/BfRh/97X0c=
			</data>
		</dict>
		<key>ExpoDevice_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tToTxnvcjrWNM4XKnhCeb9IpxIm4oaNfJxxIKQ6bFBk=
			</data>
		</dict>
		<key>ExpoDevice_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			2JioNW3Ie3zSsXkh1opGNtQkBns6dcg7eTX9eXcZycs=
			</data>
		</dict>
		<key>ExpoFileSystem_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			tIbLLVYPOp8/IhwUCD2W94RvTVECiRhZzdtwM7jXCIc=
			</data>
		</dict>
		<key>ExpoFileSystem_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			M7DgdPJz9YimS6VjKQnGqM8fFtuOTNhMaXi9Ii39Zb0=
			</data>
		</dict>
		<key>ExpoNotifications_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			id7GMWfupXibFnQPoSnDxAs3Yr2vlU6KziBhcFNBw+8=
			</data>
		</dict>
		<key>ExpoNotifications_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Ky2O23HVHFsfGs5M2yipS68i/d6bvy4r/BfRh/97X0c=
			</data>
		</dict>
		<key>ExpoSystemUI_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			P+cYRt0B74ISROxqLZgLfOXKs6tgYhM0Mg2TNT//7PY=
			</data>
		</dict>
		<key>ExpoSystemUI_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Ky2O23HVHFsfGs5M2yipS68i/d6bvy4r/BfRh/97X0c=
			</data>
		</dict>
		<key>ExpoTaskManager_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Vcwap8d0h49auUXvxL5hI7LrwLaUSbLI/1Uh5Ivqkwk=
			</data>
		</dict>
		<key>ExpoTaskManager_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Ky2O23HVHFsfGs5M2yipS68i/d6bvy4r/BfRh/97X0c=
			</data>
		</dict>
		<key>LottiePrivacyInfo.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			i3k6s6GaZzNhqmVk73l20ZjNejr4i3Fz+ixfqPgee0E=
			</data>
		</dict>
		<key>LottiePrivacyInfo.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			ENpn8heCTwGSiOwyirq8KQqwOZxS6b53ok1JQzkTfaI=
			</data>
		</dict>
		<key>Lottie_React_Native_Privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			hiFeJGBUYoIMBgKRCAe2hzI1+/n0yumyABkBZcUqUc0=
			</data>
		</dict>
		<key>Lottie_React_Native_Privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			Uh6274Qwdz5cAQ4YOP6d2PpdYre3bRzqjX2NqtyxROI=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			0iT0B29OMhTbiXYnweoynu+q6Im8gta4P/eeuquI8zU=
			</data>
		</dict>
		<key>RCT-Folly_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			JzhN/ENauoWRQrJ0ArKVGbXFPoVZn2QXHGhqoNdhUfU=
			</data>
		</dict>
		<key>RNCAsyncStorage_resources.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			4lQwnQSN9smC7mrNKJ74LE1XoL6EllhsaI53lUsjBHA=
			</data>
		</dict>
		<key>RNCAsyncStorage_resources.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			CxKH0OaGz4Z9ONl8yl3GlLMCnyfE/kJw3UvS/VRYovA=
			</data>
		</dict>
		<key>RNDeviceInfoPrivacyInfo.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			BIgecOQNZzYI3lR8dIzoPTVt+h+qRhd4yioEe0L8dzQ=
			</data>
		</dict>
		<key>RNDeviceInfoPrivacyInfo.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			4Ye67kwWytlqe/PE3iAZKFQmf1/L4XhtOgUgWAH3aeY=
			</data>
		</dict>
		<key>ReachabilitySwift.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			kWP8WHq9mLod/rj0A3JnpmZjPnB5lBxS25lYPHJr7fo=
			</data>
		</dict>
		<key>ReachabilitySwift.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			pwgfFQbJDo5nMQDlwWHnZbi3piREbiC9S9bvrKgICLg=
			</data>
		</dict>
		<key>React-Core_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			1qOaLnRjhHG0HmUvhOtd2yJMot6iWLvbfYHxFq2Dr0g=
			</data>
		</dict>
		<key>React-Core_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			fAiWnkWWIabs7AQ8zbkBJlHxhYEZRfdVF8Yj2uLhFic=
			</data>
		</dict>
		<key>React-cxxreact_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			P/UK1HL84ZIO2AEpBjS+03F9nd9XuG3qji5BopmTysY=
			</data>
		</dict>
		<key>React-cxxreact_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BYfRVcVqb08GR+vGtqC9AmYVzWEO6PIJqXhrealq0zU=
			</data>
		</dict>
		<key>SplashScreen.storyboardc/EXPO-VIEWCONTROLLER-1-view-EXPO-ContainerView.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			k4ydNrb+KUt3uZwdnO59fXEOp5AFUFkGrUv/4ujN1CM=
			</data>
		</dict>
		<key>SplashScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			dNvO7WzwpeXGmDR5MyjJeD7Ksd5ILUlU4lfKITK3Q68=
			</data>
		</dict>
		<key>SplashScreen.storyboardc/SplashScreenViewController.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			nBo0wSHSJHlAjPDqrLNJFUjO0WZVeZuuO19/I4AxS6g=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			6vyvb9FIGqg0PF0zuBJsUk+pUjNUSc3YCoZfyRuRdWw=
			</data>
		</dict>
		<key>boost_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Ki/3TSNiaUHq2ChRwK5r5nMDdhW1JsVEXhcxBVvB8EM=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash2</key>
			<data>
			rtY6m47ZiqRPGG9pATB8v86sgrHe+xqy2DVAFIrtiAk=
			</data>
		</dict>
		<key>glog_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			eNRSQgwBg/GJP0vI1SKoREI/lQJQr7P1SKXOkeD4CV4=
			</data>
		</dict>
		<key>ip.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			QteIroYp/GcBTXdkGKzGBei11nSz2Vq6lbbgYW3nV2g=
			</data>
		</dict>
		<key>sound1.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			LvgRs8NGb78nonadCAEsT3L2mdYRy72bqJrxNsbhB4Y=
			</data>
		</dict>
		<key>sound2.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			9xwpm6BvvWXfdnopCTViymbTkNSGHgQQbTk+aVIbMkY=
			</data>
		</dict>
		<key>sound3.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			VRyemjItKe+yRYK3Rl2MVgUzi9Jj1chOuTwUjPBkjUg=
			</data>
		</dict>
		<key>sound4.wav</key>
		<dict>
			<key>hash2</key>
			<data>
			CEt4Rj4w2uSqxe2/npyTze5ezkpKaINS5qK2aG+bH0o=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
