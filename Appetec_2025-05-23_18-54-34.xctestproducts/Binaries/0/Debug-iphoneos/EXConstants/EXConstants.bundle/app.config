{"name": "Appetec", "slug": "appetec", "version": "1.0.0", "scheme": "appetec", "orientation": "portrait", "icon": "./assets/adaptive-icon.png", "userInterfaceStyle": "light", "owner": "appetec", "androidStatusBar": {"backgroundColor": "#12705F", "barStyle": "dark-content"}, "jsEngine": "hermes", "newArchEnabled": true, "splash": {"image": "./assets/Appetec_logo.png", "resizeMode": "cover", "backgroundColor": "#12705F"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.appetec.app", "buildNumber": "1", "jsEngine": "jsc", "softwareKeyboardLayoutMode": "pan", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSHealthShareUsageDescription": "Allow $(PRODUCT_NAME) to access your health data.", "NSHealthUpdateUsageDescription": "Allow $(PRODUCT_NAME) to update your health data.", "NSHealthClinicalHealthRecordsShareUsageDescription": "Allow $(PRODUCT_NAME) to access your clinical health data."}, "icon": {"dark": "./assets/adaptive-icon_ios.png", "light": "./assets/adaptive-icon_ios.png"}}, "android": {"package": "com.appetec.app", "versionCode": 1, "minSdkVersion": 26, "googleServicesFile": "./google-services.json", "softwareKeyboardLayoutMode": "pan", "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "*.microcosmworks.com", "pathPrefix": "/"}], "category": ["BROWSABLE", "DEFAULT"]}], "adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon_ios.png", "backgroundColor": "#12705F"}, "permissions": ["android.permission.health.READ_ACTIVE_CALORIES_BURNED", "android.permission.health.READ_BASAL_BODY_TEMPERATURE", "android.permission.health.READ_BASAL_METABOLIC_RATE", "android.permission.health.READ_BLOOD_GLUCOSE", "android.permission.health.READ_BLOOD_PRESSURE", "android.permission.health.READ_BODY_FAT", "android.permission.health.READ_BODY_TEMPERATURE", "android.permission.health.READ_BONE_MASS", "android.permission.health.READ_CERVICAL_MUCUS", "android.permission.health.READ_EXERCISE", "android.permission.health.WRITE_DISTANCE", "android.permission.health.READ_DISTANCE", "android.permission.health.READ_ELEVATION_GAINED", "android.permission.health.READ_FLOORS_CLIMBED", "android.permission.health.WRITE_FLOORS_CLIMBED", "android.permission.health.READ_HEART_RATE", "android.permission.health.READ_HEIGHT", "android.permission.health.READ_HYDRATION", "android.permission.health.READ_LEAN_BODY_MASS", "android.permission.health.READ_MENSTRUATION", "android.permission.health.READ_NUTRITION", "android.permission.health.READ_OVULATION_TEST", "android.permission.health.READ_OXYGEN_SATURATION", "android.permission.health.READ_POWER", "android.permission.health.READ_RESPIRATORY_RATE", "android.permission.health.READ_RESTING_HEART_RATE", "android.permission.health.READ_SEXUAL_ACTIVITY", "android.permission.health.READ_SLEEP", "android.permission.health.READ_SPEED", "android.permission.health.READ_STEPS", "android.permission.health.WRITE_STEPS", "android.permission.health.READ_TOTAL_CALORIES_BURNED", "android.permission.health.READ_VO2_MAX", "android.permission.health.READ_WEIGHT", "android.permission.health.READ_WHEELCHAIR_PUSHES", "android.permission.FOREGROUND_SERVICE_HEALTH", "android.permission.health.READ_HEALTH_DATA_IN_BACKGROUND", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.RECORD_AUDIO", "android.permission.READ_CONTACTS", "android.permission.WRITE_CONTACTS", "android.permission.READ_CALENDAR", "android.permission.WRITE_CALENDAR", "android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.CAMERA", "android.permission.FOREGROUND_SERVICE", "android.permission.WAKE_LOCK"]}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "30992849-9706-4007-9214-d26f9b19a7d4"}}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."}], ["expo-image-picker", {"photosPermission": "App uses photo access to upload selected photo."}], ["expo-contacts", {"contactsPermission": "Allow $(PRODUCT_NAME) to access your contacts."}], ["expo-calendar", {"calendarPermission": "Allow $(PRODUCT_NAME) to access your calendar."}], ["expo-sensors", {"motionPermission": "Allow $(PRODUCT_NAME) to use the device motion sensor."}], ["expo-notifications", {"notificationPermission": "Allow $(PRODUCT_NAME) to send you notifications.", "icon": "./assets/notification-icon.png", "color": "#12705F", "sounds": ["./assets/sound1.wav", "./assets/sound2.wav", "./assets/sound3.wav", "./assets/sound4.wav"]}], ["expo-apple-authentication"], ["expo-av", {"microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone."}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "recordAudioAndroid": false}], ["expo-build-properties", {"android": {"compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0", "minSdkVersion": 26}, "ios": {"infoPlist": {"NSCameraUsageDescription": "Allow $(PRODUCT_NAME) to access your camera", "NSMicrophoneUsageDescription": "Allow $(PRODUCT_NAME) to access your microphone", "NSHealthShareUsageDescription": "Allow $(PRODUCT_NAME) to access your health data", "NSHealthUpdateUsageDescription": "Allow $(PRODUCT_NAME) to update your health data"}}}], ["expo-secure-store", {"configureAndroidBackup": true, "faceIDPermission": "Allow $(PRODUCT_NAME) to access your Face ID biometric data."}], ["expo-document-picker", {"iCloudContainerEnvironment": "Production"}], ["react-native-health-connect", {"package": "com.health.connect"}], ["react-native-health", {"healthSharePermission": "Allow $(PRODUCT_NAME) to access your health data.", "healthUpdatePermission": "Allow $(PRODUCT_NAME) to update your health data.", "isClinicalDataEnabled": true, "healthClinicalDescription": "Allow $(PRODUCT_NAME) to access your clinical health data."}], ["expo-splash-screen", {"image": "./assets/Appetec_logo.png", "resizeMode": "contain", "backgroundColor": "#12705F", "imageWidth": 200}], "./androidManifestPlugin.js", "expo-font", "expo-asset", "expo-health-connect", "react-native-health"], "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/30992849-9706-4007-9214-d26f9b19a7d4"}, "sdkVersion": "52.0.0", "platforms": ["ios", "android"]}